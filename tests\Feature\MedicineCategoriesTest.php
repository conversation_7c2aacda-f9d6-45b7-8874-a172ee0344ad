<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class MedicineCategoriesTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_can_create_predefined_medicine_categories(): void
    {
        /** @var User $admin */
        $admin = User::factory()->create(['role' => 'admin']);

        $medicineCategoryNames = [
            'Painkillers',
            'Antibiotics',
            'Antihistamines',
            'Vitamins & Supplements',
        ];

        foreach ($medicineCategoryNames as $categoryName) {
            $response = $this->actingAs($admin)
                ->post('/categories', ['name' => $categoryName]);

            $response->assertRedirect();
            $this->assertDatabaseHas('categories', ['name' => $categoryName]);
        }
    }

    public function test_category_names_are_unique(): void
    {
        /** @var User $admin */
        $admin = User::factory()->create(['role' => 'admin']);

        // Create a category
        Category::factory()->create(['name' => 'Painkillers']);

        // Try to create another with the same name
        $response = $this->actingAs($admin)
            ->post('/categories', ['name' => 'Painkillers']);

        $response->assertSessionHasErrors('name');
    }

    public function test_staff_cannot_create_categories(): void
    {
        /** @var User $staff */
        $staff = User::factory()->create(['role' => 'cashier']);

        $response = $this->actingAs($staff)
            ->post('/categories', ['name' => 'Antibiotics']);

        $response->assertForbidden();
    }
}
