import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import ChartWrapper from '@/components/ui/chart-wrapper';
import { DatePicker, DatePickerDemo } from '@/components/ui/date-picker';
import { useState } from 'react';

export default function ComponentsTest() {
    const [date, setDate] = useState<Date>();

    // Sample chart data
    const chartOptions = {
        chart: {
            type: 'line' as const,
            height: 350,
            toolbar: { show: false },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 400,
            },
        },
        stroke: { curve: 'smooth' as const, width: 2 },
        xaxis: { categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep'] },
        colors: ['#3B82F6'],
        legend: { position: 'top' as const },
        dataLabels: { enabled: false },
    };

    const chartSeries = [{ name: 'Sample Data', data: [10, 41, 35, 51, 49, 62, 69, 91, 148] }];

    return (
        <div className="space-y-6 p-6">
            <div>
                <h1 className="mb-4 text-2xl font-bold">Component Tests</h1>

                {/* Date Picker Tests */}
                <Card className="mb-6">
                    <CardHeader>
                        <CardTitle>Date Picker Components</CardTitle>
                        <CardDescription>Testing the updated date picker implementation</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div>
                            <h3 className="mb-2 text-lg font-semibold">Standard Date Picker</h3>
                            <DatePicker date={date} onSelect={setDate} placeholder="Pick a date" />
                        </div>

                        <div>
                            <h3 className="mb-2 text-lg font-semibold">Demo Date Picker (Shadcn Style)</h3>
                            <DatePickerDemo />
                        </div>
                    </CardContent>
                </Card>

                {/* Chart Test */}
                <Card>
                    <CardHeader>
                        <CardTitle>Optimized Chart Component</CardTitle>
                        <CardDescription>Testing the performance-optimized chart with lazy loading</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <ChartWrapper options={chartOptions} series={chartSeries} type="line" height={350} />
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}
