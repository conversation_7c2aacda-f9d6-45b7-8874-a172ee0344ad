import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { index as medicinesIndex } from '@/routes/medicines';
import { type BreadcrumbItem, type SharedData } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { format } from 'date-fns';
import { Edit, Plus, Trash2 } from 'lucide-react';
import { useState } from 'react';

interface Medicine {
    id: number;
    name: string;
    code: string;
    category: { id: number; name: string };
    supplier: { id: number; name: string };
}

interface Batch {
    id: number;
    batch_number: string;
    expiry_date: string;
    quantity: number;
    created_at: string;
}

interface BatchesIndexProps {
    medicine: Medicine;
    batches: {
        data: Batch[];
        links: any[];
        meta: any;
    };
}

export default function BatchesIndex({ medicine, batches }: BatchesIndexProps) {
    const { auth } = usePage<SharedData>().props;
    const isAdmin = auth.user?.role === 'admin';
    
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
    const [editingBatch, setEditingBatch] = useState<Batch | null>(null);
    const [formData, setFormData] = useState({
        batch_number: '',
        expiry_date: '',
        quantity: '',
    });

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Medicines', href: medicinesIndex().url },
        { title: medicine.name, href: '#' },
        { title: 'Batches', href: '#' },
    ];

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        const data = {
            batch_number: formData.batch_number,
            expiry_date: formData.expiry_date,
            quantity: parseInt(formData.quantity),
        };

        if (editingBatch) {
            router.put(`/medicines/${medicine.id}/batches/${editingBatch.id}`, data, {
                onSuccess: () => {
                    setEditingBatch(null);
                    setFormData({ batch_number: '', expiry_date: '', quantity: '' });
                },
            });
        } else {
            router.post(`/medicines/${medicine.id}/batches`, data, {
                onSuccess: () => {
                    setIsAddDialogOpen(false);
                    setFormData({ batch_number: '', expiry_date: '', quantity: '' });
                },
            });
        }
    };

    const handleEdit = (batch: Batch) => {
        setEditingBatch(batch);
        setFormData({
            batch_number: batch.batch_number,
            expiry_date: batch.expiry_date.split('T')[0], // Format for date input
            quantity: batch.quantity.toString(),
        });
    };

    const handleDelete = (batch: Batch) => {
        if (confirm('Are you sure you want to delete this batch?')) {
            router.delete(`/medicines/${medicine.id}/batches/${batch.id}`);
        }
    };

    const resetForm = () => {
        setFormData({ batch_number: '', expiry_date: '', quantity: '' });
        setEditingBatch(null);
    };

    const isExpiringSoon = (expiryDate: string) => {
        const expiry = new Date(expiryDate);
        const today = new Date();
        const diffTime = expiry.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays <= 30 && diffDays > 0;
    };

    const isExpired = (expiryDate: string) => {
        const expiry = new Date(expiryDate);
        const today = new Date();
        return expiry < today;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`${medicine.name} - Batches`} />
            
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold">{medicine.name} - Batches</h1>
                        <p className="text-muted-foreground">
                            Code: {medicine.code} | Category: {medicine.category.name} | Supplier: {medicine.supplier.name}
                        </p>
                    </div>
                    
                    {isAdmin && (
                        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                            <DialogTrigger asChild>
                                <Button>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add Batch
                                </Button>
                            </DialogTrigger>
                            <DialogContent>
                                <DialogHeader>
                                    <DialogTitle>Add New Batch</DialogTitle>
                                    <DialogDescription>
                                        Add a new batch for {medicine.name}
                                    </DialogDescription>
                                </DialogHeader>
                                <form onSubmit={handleSubmit} className="space-y-4">
                                    <div>
                                        <Label htmlFor="batch_number">Batch Number</Label>
                                        <Input
                                            id="batch_number"
                                            value={formData.batch_number}
                                            onChange={(e) => setFormData({ ...formData, batch_number: e.target.value })}
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="expiry_date">Expiry Date</Label>
                                        <Input
                                            id="expiry_date"
                                            type="date"
                                            value={formData.expiry_date}
                                            onChange={(e) => setFormData({ ...formData, expiry_date: e.target.value })}
                                            required
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="quantity">Quantity</Label>
                                        <Input
                                            id="quantity"
                                            type="number"
                                            min="1"
                                            value={formData.quantity}
                                            onChange={(e) => setFormData({ ...formData, quantity: e.target.value })}
                                            required
                                        />
                                    </div>
                                    <div className="flex justify-end space-x-2">
                                        <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                                            Cancel
                                        </Button>
                                        <Button type="submit">Add Batch</Button>
                                    </div>
                                </form>
                            </DialogContent>
                        </Dialog>
                    )}
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Batch Inventory</CardTitle>
                        <CardDescription>
                            Manage batches for {medicine.name}. Batches are sorted by expiry date (FIFO).
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {batches.data.length === 0 ? (
                            <div className="text-center py-8">
                                <p className="text-muted-foreground">No batches found for this medicine.</p>
                                {isAdmin && (
                                    <Button className="mt-4" onClick={() => setIsAddDialogOpen(true)}>
                                        <Plus className="mr-2 h-4 w-4" />
                                        Add First Batch
                                    </Button>
                                )}
                            </div>
                        ) : (
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Batch Number</TableHead>
                                        <TableHead>Expiry Date</TableHead>
                                        <TableHead>Quantity</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Added</TableHead>
                                        {isAdmin && <TableHead>Actions</TableHead>}
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {batches.data.map((batch) => (
                                        <TableRow key={batch.id}>
                                            <TableCell className="font-medium">{batch.batch_number}</TableCell>
                                            <TableCell>
                                                {format(new Date(batch.expiry_date), 'MMM dd, yyyy')}
                                            </TableCell>
                                            <TableCell>{batch.quantity}</TableCell>
                                            <TableCell>
                                                {isExpired(batch.expiry_date) ? (
                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                        Expired
                                                    </span>
                                                ) : isExpiringSoon(batch.expiry_date) ? (
                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                        Expiring Soon
                                                    </span>
                                                ) : (
                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        Good
                                                    </span>
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                {format(new Date(batch.created_at), 'MMM dd, yyyy')}
                                            </TableCell>
                                            {isAdmin && (
                                                <TableCell>
                                                    <div className="flex space-x-2">
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => handleEdit(batch)}
                                                        >
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => handleDelete(batch)}
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            )}
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        )}
                    </CardContent>
                </Card>

                {/* Edit Dialog */}
                <Dialog open={!!editingBatch} onOpenChange={(open) => !open && resetForm()}>
                    <DialogContent>
                        <DialogHeader>
                            <DialogTitle>Edit Batch</DialogTitle>
                            <DialogDescription>
                                Update batch information for {medicine.name}
                            </DialogDescription>
                        </DialogHeader>
                        <form onSubmit={handleSubmit} className="space-y-4">
                            <div>
                                <Label htmlFor="edit_batch_number">Batch Number</Label>
                                <Input
                                    id="edit_batch_number"
                                    value={formData.batch_number}
                                    onChange={(e) => setFormData({ ...formData, batch_number: e.target.value })}
                                    required
                                />
                            </div>
                            <div>
                                <Label htmlFor="edit_expiry_date">Expiry Date</Label>
                                <Input
                                    id="edit_expiry_date"
                                    type="date"
                                    value={formData.expiry_date}
                                    onChange={(e) => setFormData({ ...formData, expiry_date: e.target.value })}
                                    required
                                />
                            </div>
                            <div>
                                <Label htmlFor="edit_quantity">Quantity</Label>
                                <Input
                                    id="edit_quantity"
                                    type="number"
                                    min="0"
                                    value={formData.quantity}
                                    onChange={(e) => setFormData({ ...formData, quantity: e.target.value })}
                                    required
                                />
                            </div>
                            <div className="flex justify-end space-x-2">
                                <Button type="button" variant="outline" onClick={resetForm}>
                                    Cancel
                                </Button>
                                <Button type="submit">Update Batch</Button>
                            </div>
                        </form>
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
}
