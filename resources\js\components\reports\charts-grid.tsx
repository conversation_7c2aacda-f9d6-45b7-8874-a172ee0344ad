import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import ChartWrapper from '@/components/ui/chart-wrapper';
import type { DailySales, MonthlySales, PaymentMethod } from '@/types/reports';
import { BarChart3 } from 'lucide-react';
import { useMemo } from 'react';

interface ChartsGridProps {
    dailySales: DailySales;
    paymentMethods: PaymentMethod[];
    monthlyComparison: MonthlySales;
}

export default function ChartsGrid({ dailySales, paymentMethods, monthlyComparison }: ChartsGridProps) {
    // Memoize chart configurations to prevent unnecessary re-renders
    const dailySalesChartOptions = useMemo(
        () => ({
            chart: {
                type: 'line' as const,
                height: 350,
                toolbar: { show: false },
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 400,
                },
            },
            stroke: { curve: 'smooth' as const, width: 2 },
            xaxis: { categories: dailySales.dates },
            yaxis: [{ title: { text: 'Sales Count' } }, { opposite: true, title: { text: 'Revenue ($)' } }],
            colors: ['#3B82F6', '#10B981'],
            legend: { position: 'top' as const },
            dataLabels: { enabled: false },
        }),
        [dailySales.dates],
    );

    const dailySalesChartSeries = useMemo(
        () => [
            { name: 'Sales Count', type: 'line', data: dailySales.sales_counts },
            { name: 'Revenue', type: 'line', yAxisIndex: 1, data: dailySales.revenues },
        ],
        [dailySales.sales_counts, dailySales.revenues],
    );

    const paymentMethodsChartOptions = useMemo(
        () => ({
            chart: {
                type: 'donut' as const,
                height: 350,
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 400,
                },
            },
            labels: paymentMethods.map((pm) => pm.method),
            colors: ['#3B82F6', '#10B981', '#F59E0B'],
            legend: { position: 'bottom' as const },
            dataLabels: { enabled: true },
            plotOptions: {
                pie: {
                    donut: {
                        size: '70%',
                    },
                },
            },
        }),
        [paymentMethods],
    );

    const paymentMethodsChartSeries = useMemo(() => paymentMethods.map((pm) => pm.total_amount), [paymentMethods]);

    const monthlyComparisonChartOptions = useMemo(
        () => ({
            chart: {
                type: 'bar' as const,
                height: 350,
                toolbar: { show: false },
                animations: {
                    enabled: true,
                    easing: 'easeinout',
                    speed: 400,
                },
            },
            xaxis: { categories: monthlyComparison.months },
            yaxis: [{ title: { text: 'Sales Count' } }, { opposite: true, title: { text: 'Revenue ($)' } }],
            colors: ['#8B5CF6', '#EF4444'],
            legend: { position: 'top' as const },
            dataLabels: { enabled: false },
            plotOptions: {
                bar: {
                    borderRadius: 4,
                    dataLabels: {
                        position: 'top',
                    },
                },
            },
        }),
        [monthlyComparison.months],
    );

    const monthlyComparisonChartSeries = useMemo(
        () => [
            { name: 'Sales Count', data: monthlyComparison.sales_counts },
            { name: 'Revenue', data: monthlyComparison.revenues },
        ],
        [monthlyComparison.sales_counts, monthlyComparison.revenues],
    );

    return (
        <>
            {/* Charts Row 1 */}
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center space-x-2">
                            <BarChart3 className="h-5 w-5" />
                            <span>Daily Sales Trend</span>
                        </CardTitle>
                        <CardDescription>Sales count and revenue over time</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <ChartWrapper options={dailySalesChartOptions} series={dailySalesChartSeries} type="line" height={350} />
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>Payment Methods Distribution</CardTitle>
                        <CardDescription>Revenue breakdown by payment method</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <ChartWrapper options={paymentMethodsChartOptions} series={paymentMethodsChartSeries} type="donut" height={350} />
                    </CardContent>
                </Card>
            </div>

            {/* Charts Row 2 */}
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-1">
                <Card>
                    <CardHeader>
                        <CardTitle>Monthly Comparison</CardTitle>
                        <CardDescription>Sales performance over recent months</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <ChartWrapper options={monthlyComparisonChartOptions} series={monthlyComparisonChartSeries} type="bar" height={350} />
                    </CardContent>
                </Card>
            </div>
        </>
    );
}
