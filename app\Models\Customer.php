<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    /** @use HasFactory<\Database\Factories\CustomerFactory> */
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'date_of_birth',
        'gender',
        'notes',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
    ];

    /** @return HasMany<Sale> */
    public function sales(): HasMany
    {
        return $this->hasMany(Sale::class);
    }

    /**
     * Get the customer's full name with phone for display.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->phone ? "{$this->name} ({$this->phone})" : $this->name;
    }

    /**
     * Get total amount spent by customer.
     */
    public function getTotalSpentAttribute(): float
    {
        return $this->sales()->sum('total');
    }

    /**
     * Get total number of purchases.
     */
    public function getTotalPurchasesAttribute(): int
    {
        return $this->sales()->count();
    }
}
