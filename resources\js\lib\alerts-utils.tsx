import type { AlertType } from '@/types/alerts';
import { AlertTriangle, Package, PackageX, Timer, XCircle } from 'lucide-react';

export const getAlertIcon = (type: AlertType) => {
    switch (type) {
        case 'out_of_stock':
            return <PackageX className="h-5 w-5 text-red-500" />;
        case 'low_stock':
            return <Package className="h-5 w-5 text-yellow-500" />;
        case 'expired':
            return <XCircle className="h-5 w-5 text-red-500" />;
        case 'expiring':
            return <Timer className="h-5 w-5 text-orange-500" />;
        default:
            return <AlertTriangle className="h-5 w-5 text-gray-500" />;
    }
};

export const getDaysUntilExpiry = (expiryDate: string): number => {
    const expiry = new Date(expiryDate);
    const today = new Date();
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
};

export const getStockStatusColor = (type: 'out_of_stock' | 'low_stock'): string => {
    switch (type) {
        case 'out_of_stock':
            return 'bg-red-100 text-red-800';
        case 'low_stock':
            return 'bg-yellow-100 text-yellow-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

export const getExpiryStatusColor = (type: 'expired' | 'expiring'): string => {
    switch (type) {
        case 'expired':
            return 'bg-red-100 text-red-800';
        case 'expiring':
            return 'bg-orange-100 text-orange-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};
