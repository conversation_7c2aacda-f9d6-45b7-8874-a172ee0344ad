'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from '@inertiajs/react';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const formSchema = z.object({
    name: z.string().min(1, { message: 'Please select a category name.' }),
});

// Predefined medicine categories
const MEDICINE_CATEGORIES = [
    'Painkillers',
    'Antibiotics',
    'Antihistamines',
    'Antacids',
    'Vitamins & Supplements',
    'Antipyretics',
    'Anti-inflammatory',
    'Cough & Cold',
    'Digestive Health',
    'Heart & Blood Pressure',
    'Diabetes Medications',
    'Skin Care',
    'Eye Care',
    'Respiratory',
    'Antifungal',
    'Antiviral',
    'Hormones',
    'Mental Health',
    'Vaccines',
    'First Aid',
] as const;

interface EditCategoryFormProps {
    category: {
        id: number;
        name: string;
    };
    trigger?: React.ReactNode;
}

export function EditCategoryForm({ category, trigger }: EditCategoryFormProps) {
    const [open, setOpen] = useState(false);

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: category.name,
        },
    });

    // Reset form when category changes
    useEffect(() => {
        form.reset({ name: category.name });
    }, [category, form]);

    function onSubmit(values: z.infer<typeof formSchema>) {
        router.put(`/categories/${category.id}`, values, {
            onSuccess: () => {
                setOpen(false);
            },
            onError: (errors) => {
                // Handle validation errors from server
                if (errors.name) {
                    form.setError('name', { message: errors.name });
                }
            },
        });
    }

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                {trigger || (
                    <Button variant="outline" size="sm">
                        Edit
                    </Button>
                )}
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Edit Category</DialogTitle>
                    <DialogDescription>Update the medicine category name.</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Category Name</FormLabel>
                                    <Select onValueChange={field.onChange} value={field.value}>
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select a medicine category" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {MEDICINE_CATEGORIES.map((categoryName) => (
                                                <SelectItem key={categoryName} value={categoryName}>
                                                    {categoryName}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <div className="flex justify-end space-x-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => {
                                    setOpen(false);
                                    form.reset({ name: category.name });
                                }}
                            >
                                Cancel
                            </Button>
                            <Button type="submit" disabled={form.formState.isSubmitting}>
                                {form.formState.isSubmitting ? 'Updating...' : 'Update Category'}
                            </Button>
                        </div>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
