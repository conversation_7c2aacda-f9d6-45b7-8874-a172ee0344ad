<?php

namespace App\Services;

use App\Models\Batch;
use App\Models\Medicine;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

class AlertService
{
    /**
     * Get medicines with low stock.
     */
    public function getLowStockMedicines(int $threshold = 10): Collection
    {
        return Medicine::query()
            ->where('stock_quantity', '<=', $threshold)
            ->where('stock_quantity', '>', 0)
            ->with(['category:id,name', 'supplier:id,name'])
            ->orderBy('stock_quantity')
            ->get();
    }

    /**
     * Get medicines that are out of stock.
     */
    public function getOutOfStockMedicines(): Collection
    {
        return Medicine::query()
            ->where('stock_quantity', '<=', 0)
            ->with(['category:id,name', 'supplier:id,name'])
            ->orderBy('name')
            ->get();
    }

    /**
     * Get batches that are expiring soon.
     */
    public function getExpiringSoonBatches(int $days = 30): Collection
    {
        $expiryDate = Carbon::now()->addDays($days);

        return Batch::query()
            ->where('expiry_date', '<=', $expiryDate)
            ->where('expiry_date', '>', Carbon::now())
            ->where('quantity', '>', 0)
            ->with(['medicine:id,name,code', 'medicine.category:id,name'])
            ->orderBy('expiry_date')
            ->get();
    }

    /**
     * Get batches that have already expired.
     */
    public function getExpiredBatches(): Collection
    {
        return Batch::query()
            ->where('expiry_date', '<', Carbon::now())
            ->where('quantity', '>', 0)
            ->with(['medicine:id,name,code', 'medicine.category:id,name'])
            ->orderBy('expiry_date')
            ->get();
    }

    /**
     * Get all alerts summary.
     */
    public function getAlertsSummary(int $lowStockThreshold = 10, int $expiryDays = 30): array
    {
        return [
            'low_stock_count' => $this->getLowStockMedicines($lowStockThreshold)->count(),
            'out_of_stock_count' => $this->getOutOfStockMedicines()->count(),
            'expiring_soon_count' => $this->getExpiringSoonBatches($expiryDays)->count(),
            'expired_count' => $this->getExpiredBatches()->count(),
            'total_alerts' => $this->getLowStockMedicines($lowStockThreshold)->count() +
                $this->getOutOfStockMedicines()->count() +
                $this->getExpiringSoonBatches($expiryDays)->count() +
                $this->getExpiredBatches()->count(),
        ];
    }

    /**
     * Get critical alerts that need immediate attention.
     */
    public function getCriticalAlerts(): array
    {
        return [
            'out_of_stock' => $this->getOutOfStockMedicines(),
            'expired' => $this->getExpiredBatches(),
            'critical_low_stock' => $this->getLowStockMedicines(5), // Very low threshold
        ];
    }
}
