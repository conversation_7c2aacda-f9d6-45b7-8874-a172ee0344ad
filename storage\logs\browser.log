[2025-08-26 04:43:59] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183438626:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183438626:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183438626:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:43:58.654Z"} 
[2025-08-26 04:43:59] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:43:58.656Z"} 
[2025-08-26 04:43:59] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183438626:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183438626:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183438626:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:43:58.661Z"} 
[2025-08-26 04:43:59] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:43:58.662Z"} 
[2025-08-26 04:43:59] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183438774:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183438774:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183438774:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:43:58.802Z"} 
[2025-08-26 04:43:59] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:43:58.803Z"} 
[2025-08-26 04:43:59] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183438774:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183438774:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183438774:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:43:58.803Z"} 
[2025-08-26 04:43:59] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:43:58.804Z"} 
[2025-08-26 04:44:14] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183453774:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183453774:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183453774:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:13.800Z"} 
[2025-08-26 04:44:14] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:13.801Z"} 
[2025-08-26 04:44:14] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183453774:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183453774:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183453774:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:13.804Z"} 
[2025-08-26 04:44:14] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:13.804Z"} 
[2025-08-26 04:44:14] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183453926:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183453926:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183453926:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:13.943Z"} 
[2025-08-26 04:44:14] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:13.944Z"} 
[2025-08-26 04:44:14] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183453926:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183453926:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183453926:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:13.945Z"} 
[2025-08-26 04:44:14] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:13.945Z"} 
[2025-08-26 04:44:26] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:25.202Z"} 
[2025-08-26 04:44:26] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183465292:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183465292:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183465292:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:25.320Z"} 
[2025-08-26 04:44:26] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:25.320Z"} 
[2025-08-26 04:44:26] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183465292:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183465292:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183465292:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:25.321Z"} 
[2025-08-26 04:44:26] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:25.321Z"} 
[2025-08-26 04:44:26] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183465430:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183465430:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183465430:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:25.465Z"} 
[2025-08-26 04:44:26] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:25.466Z"} 
[2025-08-26 04:44:26] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183465430:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183465430:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183465430:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:25.466Z"} 
[2025-08-26 04:44:26] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:25.466Z"} 
[2025-08-26 04:44:43] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183481447:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183481447:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183481447:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:41.508Z"} 
[2025-08-26 04:44:43] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:41.510Z"} 
[2025-08-26 04:44:43] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183481655:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183481655:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183481521:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:41.758Z"} 
[2025-08-26 04:44:43] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:41.759Z"} 
[2025-08-26 04:44:43] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183481655:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183481655:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183481655:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:41.866Z"} 
[2025-08-26 04:44:43] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:41.866Z"} 
[2025-08-26 04:44:43] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183481655:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183481655:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183481655:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:41.902Z"} 
[2025-08-26 04:44:43] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:44:41.902Z"} 
[2025-08-26 04:45:33] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183532805:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183532805:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183532805:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:45:32.844Z"} 
[2025-08-26 04:45:33] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:45:32.845Z"} 
[2025-08-26 04:45:33] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183532805:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183532805:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183532805:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:45:32.849Z"} 
[2025-08-26 04:45:33] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:45:32.849Z"} 
[2025-08-26 04:45:33] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183532958:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183532958:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183532958:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:45:32.998Z"} 
[2025-08-26 04:45:33] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:45:32.999Z"} 
[2025-08-26 04:45:33] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183532958:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183532958:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183532958:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:45:33.002Z"} 
[2025-08-26 04:45:33] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:45:33.002Z"} 
[2025-08-26 04:46:10] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183570090:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183570090:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183570090:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:46:10.120Z"} 
[2025-08-26 04:46:10] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:46:10.121Z"} 
[2025-08-26 04:46:10] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183570090:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183570090:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183570090:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:46:10.125Z"} 
[2025-08-26 04:46:10] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:46:10.126Z"} 
[2025-08-26 04:46:10] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183570233:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183570233:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183570233:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:46:10.335Z"} 
[2025-08-26 04:46:10] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:46:10.336Z"} 
[2025-08-26 04:46:10] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183570233:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183570233:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183570233:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:46:10.341Z"} 
[2025-08-26 04:46:10] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:46:10.341Z"} 
[2025-08-26 04:46:51] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756183609278 47 31 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183609278:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183609278:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183609278:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:46:50.122Z"} 
[2025-08-26 04:46:51] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756183609278 47 31 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183609278:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183609278:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183609278:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:46:50.123Z"} 
[2025-08-26 04:46:51] local.ERROR: Unhandled Promise Rejection ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183609278:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183609278:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183609278:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:46:50.155Z"} 
[2025-08-26 04:47:50] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183670141:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183670141:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183670141:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:47:50.163Z"} 
[2025-08-26 04:47:50] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:47:50.164Z"} 
[2025-08-26 04:47:50] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183670141:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183670141:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183670141:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:47:50.167Z"} 
[2025-08-26 04:47:50] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:47:50.167Z"} 
[2025-08-26 04:47:50] local.ERROR: [vite] Failed to reload /resources/js/components/user-menu-content.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:47:50.184Z"} 
[2025-08-26 04:47:50] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183670406:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183670406:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183670406:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:47:50.435Z"} 
[2025-08-26 04:47:50] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:47:50.436Z"} 
[2025-08-26 04:47:50] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183670406:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183670406:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183670406:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:47:50.436Z"} 
[2025-08-26 04:47:50] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:47:50.436Z"} 
[2025-08-26 04:48:01] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:00.050Z"} 
[2025-08-26 04:48:01] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:00.060Z"} 
[2025-08-26 04:48:01] local.ERROR: [vite] Failed to reload /resources/js/components/user-menu-content.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:00.062Z"} 
[2025-08-26 04:48:01] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183680273:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183680273:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183680273:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:00.313Z"} 
[2025-08-26 04:48:01] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:00.314Z"} 
[2025-08-26 04:48:01] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183680273:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183680273:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183680273:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:00.314Z"} 
[2025-08-26 04:48:01] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:00.314Z"} 
[2025-08-26 04:48:12] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183691291:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183691291:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183691291:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:11.325Z"} 
[2025-08-26 04:48:12] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:11.326Z"} 
[2025-08-26 04:48:12] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183691291:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183691291:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183691291:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:11.326Z"} 
[2025-08-26 04:48:12] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:11.326Z"} 
[2025-08-26 04:48:12] local.ERROR: [vite] Failed to reload /resources/js/components/user-menu-content.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:11.345Z"} 
[2025-08-26 04:48:12] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183691811:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183691811:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183691747:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:12.044Z"} 
[2025-08-26 04:48:12] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:12.045Z"} 
[2025-08-26 04:48:12] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183691811:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183691811:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183691811:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:12.054Z"} 
[2025-08-26 04:48:12] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:12.054Z"} 
[2025-08-26 04:48:12] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183691811:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183691811:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183691811:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:12.058Z"} 
[2025-08-26 04:48:12] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:12.058Z"} 
[2025-08-26 04:48:12] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183691811:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183691811:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183691811:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:12.060Z"} 
[2025-08-26 04:48:12] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:12.060Z"} 
[2025-08-26 04:48:25] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:24.566Z"} 
[2025-08-26 04:48:25] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:24.567Z"} 
[2025-08-26 04:48:25] local.ERROR: [vite] Failed to reload /resources/js/components/user-menu-content.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:24.569Z"} 
[2025-08-26 04:48:25] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183704868:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183704868:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183704868:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:24.968Z"} 
[2025-08-26 04:48:25] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:24.968Z"} 
[2025-08-26 04:48:25] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183704868:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183704868:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183704868:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:24.970Z"} 
[2025-08-26 04:48:25] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:24.970Z"} 
[2025-08-26 04:48:37] local.ERROR: [vite] Failed to reload /resources/js/components/user-menu-content.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:36.844Z"} 
[2025-08-26 04:48:37] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:36.847Z"} 
[2025-08-26 04:48:37] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:36.848Z"} 
[2025-08-26 04:48:37] local.ERROR: [vite] Failed to reload /resources/js/components/user-menu-content.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:36.849Z"} 
[2025-08-26 04:48:57] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:55.767Z"} 
[2025-08-26 04:48:57] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:55.767Z"} 
[2025-08-26 04:48:57] local.ERROR: [vite] Failed to reload /resources/js/components/user-menu-content.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:55.769Z"} 
[2025-08-26 04:48:57] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183736009:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:56.069Z"} 
[2025-08-26 04:48:57] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:56.070Z"} 
[2025-08-26 04:48:57] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183736009:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:56.071Z"} 
[2025-08-26 04:48:57] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:48:56.071Z"} 
[2025-08-26 04:50:04] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009 47 31 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183736009:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:50:04.304Z"} 
[2025-08-26 04:50:04] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009 47 31 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183736009:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:50:04.304Z"} 
[2025-08-26 04:50:04] local.ERROR: Unhandled Promise Rejection ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183736009:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:50:04.321Z"} 
[2025-08-26 04:50:30] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009 47 31 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183736009:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:50:29.395Z"} 
[2025-08-26 04:50:30] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009 47 31 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183736009:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:50:29.395Z"} 
[2025-08-26 04:50:30] local.ERROR: Unhandled Promise Rejection ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183736009:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:50:29.413Z"} 
[2025-08-26 04:50:51] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009 47 31 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183736009:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:50:50.333Z"} 
[2025-08-26 04:50:51] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009 47 31 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183736009:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:50:50.333Z"} 
[2025-08-26 04:50:51] local.ERROR: Unhandled Promise Rejection ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183736009:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183736009:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:50:50.351Z"} 
[2025-08-26 04:51:01] local.ERROR: [vite] Failed to reload /resources/js/components/user-menu-content.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:51:01.441Z"} 
[2025-08-26 04:52:07] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756183861705 47 31 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183861705:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183861705:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183861705:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:52:07.242Z"} 
[2025-08-26 04:52:07] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756183861705 47 31 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183861705:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183861705:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183861705:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:52:07.242Z"} 
[2025-08-26 04:52:07] local.ERROR: Unhandled Promise Rejection ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183861705:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183861705:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183861705:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:52:07.244Z"} 
[2025-08-26 04:54:24] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756183861705 47 31 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183861705:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183861705:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183861705:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:54:24.216Z"} 
[2025-08-26 04:54:24] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756183861705 47 31 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183861705:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183861705:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183861705:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:54:24.216Z"} 
[2025-08-26 04:54:24] local.ERROR: Unhandled Promise Rejection ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756183861705:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756183861705:39:18)
    at http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756183861705:28:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T04:54:24.218Z"} 
[2025-08-26 05:23:29] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:23:28.606Z"} 
[2025-08-26 05:23:29] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:23:29.146Z"} 
[2025-08-26 05:23:29] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:23:29.153Z"} 
[2025-08-26 05:23:29] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:23:29.175Z"} 
[2025-08-26 05:23:29] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:23:29.182Z"} 
[2025-08-26 05:26:12] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:26:11.813Z"} 
[2025-08-26 05:26:12] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:26:12.137Z"} 
[2025-08-26 05:26:13] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756185972310 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756185972310:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756185972310:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756185972310:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10728:43) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:26:12.837Z"} 
[2025-08-26 05:26:13] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756185972310 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756185972310:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756185972310:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756185972310:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10728:43) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:26:12.838Z"} 
[2025-08-26 05:26:13] local.WARNING: %s

%s An error occurred in the <AuthSplitLayout> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:26:12.839Z"} 
[2025-08-26 05:26:26] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:26:25.890Z"} 
[2025-08-26 05:26:26] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:26:26.192Z"} 
[2025-08-26 05:26:27] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756185986338 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756185986338:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756185986338:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756185986338:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10728:43) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:26:26.580Z"} 
[2025-08-26 05:26:27] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756185986338 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756185986338:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756185986338:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756185986338:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10728:43) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:26:26.581Z"} 
[2025-08-26 05:26:27] local.WARNING: %s

%s An error occurred in the <AuthSplitLayout> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:26:26.581Z"} 
[2025-08-26 05:26:38] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:26:37.165Z"} 
[2025-08-26 05:26:38] local.ERROR: [vite] Failed to reload /resources/js/layouts/auth/auth-split-layout.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:26:37.508Z"} 
[2025-08-26 05:26:38] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:26:37.508Z"} 
[2025-08-26 05:26:38] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:26:37.512Z"} 
[2025-08-26 05:26:38] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:26:37.523Z"} 
[2025-08-26 05:26:38] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:26:37.538Z"} 
[2025-08-26 05:27:01] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:00.777Z"} 
[2025-08-26 05:27:01] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:01.058Z"} 
[2025-08-26 05:27:02] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756186021223 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756186021223:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756186021223:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756186021223:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10728:43) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:01.614Z"} 
[2025-08-26 05:27:02] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756186021223 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756186021223:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756186021223:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756186021223:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10728:43) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:01.614Z"} 
[2025-08-26 05:27:02] local.WARNING: %s

%s An error occurred in the <AuthSplitLayout> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:01.615Z"} 
[2025-08-26 05:27:14] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:14.035Z"} 
[2025-08-26 05:27:14] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:14.284Z"} 
[2025-08-26 05:27:15] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756186034402 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756186034402:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756186034402:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756186034402:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10728:43) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:14.574Z"} 
[2025-08-26 05:27:15] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756186034402 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756186034402:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756186034402:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756186034402:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10728:43) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:14.574Z"} 
[2025-08-26 05:27:15] local.WARNING: %s

%s An error occurred in the <AuthSplitLayout> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:14.575Z"} 
[2025-08-26 05:27:36] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:36.274Z"} 
[2025-08-26 05:27:37] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756186034402 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756186034402:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756186034402:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756186034402:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10728:43) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:36.585Z"} 
[2025-08-26 05:27:37] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756186034402 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756186034402:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756186034402:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756186034402:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=366fd817:10728:43) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:36.585Z"} 
[2025-08-26 05:27:37] local.WARNING: %s

%s An error occurred in the <AuthSplitLayout> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:36.587Z"} 
[2025-08-26 05:27:37] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:36.614Z"} 
[2025-08-26 05:27:37] local.ERROR: [vite] Failed to reload /resources/js/layouts/auth/auth-split-layout.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:36.614Z"} 
[2025-08-26 05:27:37] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:36.623Z"} 
[2025-08-26 05:27:37] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:36.628Z"} 
[2025-08-26 05:27:37] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:27:36.664Z"} 
[2025-08-26 05:28:16] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/login","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:28:15.974Z"} 
[2025-08-26 05:30:42] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.376Z"} 
[2025-08-26 05:30:43] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705:39:18)
    at http://[::1]:5173/resources/js/pages/dashboard.tsx?t=1756186242705:24:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.731Z"} 
[2025-08-26 05:30:43] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.731Z"} 
[2025-08-26 05:30:43] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705 47 31 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705:39:18)
    at AppSidebar (http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756186242705:48:13)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10728:43) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.758Z"} 
[2025-08-26 05:30:43] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705 47 31 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705:39:18)
    at AppSidebar (http://[::1]:5173/resources/js/components/app-sidebar.tsx?t=1756186242705:48:13)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10728:43) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.759Z"} 
[2025-08-26 05:30:43] local.WARNING: %s

%s An error occurred in the <AppSidebar> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.760Z"} 
[2025-08-26 05:30:43] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.762Z"} 
[2025-08-26 05:30:43] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.794Z"} 
[2025-08-26 05:30:43] local.ERROR: [vite] Failed to reload /resources/js/components/user-menu-content.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.811Z"} 
[2025-08-26 05:30:43] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.823Z"} 
[2025-08-26 05:30:43] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.837Z"} 
[2025-08-26 05:30:43] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.844Z"} 
[2025-08-26 05:30:43] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.850Z"} 
[2025-08-26 05:30:43] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.858Z"} 
[2025-08-26 05:30:43] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.872Z"} 
[2025-08-26 05:30:43] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.894Z"} 
[2025-08-26 05:30:43] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756186242705:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10728:43) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.967Z"} 
[2025-08-26 05:30:43] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756186242705:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10728:43) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.967Z"} 
[2025-08-26 05:30:43] local.WARNING: %s

%s An error occurred in the <AuthSplitLayout> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.967Z"} 
[2025-08-26 05:30:43] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756186242705:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10728:43) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.995Z"} 
[2025-08-26 05:30:43] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756186242705:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756186242705:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10728:43) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.995Z"} 
[2025-08-26 05:30:43] local.WARNING: %s

%s An error occurred in the <AuthSplitLayout> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:42.995Z"} 
[2025-08-26 05:30:43] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756*********:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756*********:39:18)
    at http://[::1]:5173/resources/js/pages/dashboard.tsx?t=1756*********:24:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:43.052Z"} 
[2025-08-26 05:30:43] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:43.053Z"} 
[2025-08-26 05:30:43] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756********* 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756*********:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756*********:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756*********:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10728:43) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:43.072Z"} 
[2025-08-26 05:30:43] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756********* 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756*********:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756*********:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756*********:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10728:43) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:43.072Z"} 
[2025-08-26 05:30:43] local.WARNING: %s

%s An error occurred in the <AuthSplitLayout> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:30:43.073Z"} 
[2025-08-26 05:31:20] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:20.323Z"} 
[2025-08-26 05:31:21] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:20.923Z"} 
[2025-08-26 05:31:34] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:34.356Z"} 
[2025-08-26 05:31:36] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:34.643Z"} 
[2025-08-26 05:31:36] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:34.650Z"} 
[2025-08-26 05:31:36] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:34.656Z"} 
[2025-08-26 05:31:36] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:34.666Z"} 
[2025-08-26 05:31:36] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:34.673Z"} 
[2025-08-26 05:31:36] local.ERROR: [vite] Failed to reload /resources/js/components/user-menu-content.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:34.680Z"} 
[2025-08-26 05:31:36] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:34.685Z"} 
[2025-08-26 05:31:36] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:34.690Z"} 
[2025-08-26 05:31:36] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:34.698Z"} 
[2025-08-26 05:31:36] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:34.707Z"} 
[2025-08-26 05:31:36] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:34.712Z"} 
[2025-08-26 05:31:36] local.ERROR: [vite] Failed to reload /resources/js/components/app-sidebar.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:34.732Z"} 
[2025-08-26 05:31:36] local.ERROR: [vite] ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at dashboard.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756*********:47:31)
    at dashboard (http://[::1]:5173/resources/js/routes/index.ts?t=1756*********:39:18)
    at http://[::1]:5173/resources/js/pages/dashboard.tsx?t=1756*********:24:11 {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:34.885Z"} 
[2025-08-26 05:31:36] local.ERROR: [vite] Failed to reload /resources/js/pages/dashboard.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:34.886Z"} 
[2025-08-26 05:31:36] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756********* 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756*********:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756*********:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756*********:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10728:43) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:35.586Z"} 
[2025-08-26 05:31:36] local.ERROR: Uncaught ReferenceError: queryParams is not defined http://[::1]:5173/resources/js/routes/index.ts?t=1756********* 10 26 ReferenceError queryParams is not defined ReferenceError: queryParams is not defined
    at home.url (http://[::1]:5173/resources/js/routes/index.ts?t=1756*********:10:26)
    at home (http://[::1]:5173/resources/js/routes/index.ts?t=1756*********:2:13)
    at AuthSplitLayout (http://[::1]:5173/resources/js/layouts/auth/auth-split-layout.tsx?t=1756*********:31:44)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=0630900b:10728:43) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:35.586Z"} 
[2025-08-26 05:31:36] local.WARNING: %s

%s An error occurred in the <AuthSplitLayout> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:35.586Z"} 
[2025-08-26 05:31:56] local.ERROR: [vite] Failed to reload /resources/js/pages/auth/login.tsx. This could be due to syntax errors or importing non-existent modules. (see errors above) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:31:55.224Z"} 
[2025-08-26 05:43:07] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T05:43:05.916Z"} 
[2025-08-26 06:02:39] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'user') http://[::1]:5173/resources/js/pages/medicines/index.tsx 27 24 TypeError Cannot read properties of undefined (reading 'user') TypeError: Cannot read properties of undefined (reading 'user')
    at MedicinesIndex (http://[::1]:5173/resources/js/pages/medicines/index.tsx:27:24)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:10359:46) {"url":"http://127.0.0.1:8000/medicines","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T06:02:39.594Z"} 
[2025-08-26 06:02:39] local.ERROR: Uncaught TypeError: Cannot read properties of undefined (reading 'user') http://[::1]:5173/resources/js/pages/medicines/index.tsx 27 24 TypeError Cannot read properties of undefined (reading 'user') TypeError: Cannot read properties of undefined (reading 'user')
    at MedicinesIndex (http://[::1]:5173/resources/js/pages/medicines/index.tsx:27:24)
    at Object.react_stack_bottom_frame (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:17424:20)
    at renderWithHooks (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:4206:24)
    at updateFunctionComponent (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:6619:21)
    at beginWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:7654:20)
    at runWithFiberInDEV (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:1485:72)
    at performUnitOfWork (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:10868:98)
    at workLoopSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:10728:43)
    at renderRootSync (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:10711:13)
    at performWorkOnRoot (http://[::1]:5173/node_modules/.vite/deps/react-dom_client.js?v=aee33a9d:10359:46) {"url":"http://127.0.0.1:8000/medicines","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T06:02:39.595Z"} 
[2025-08-26 06:02:39] local.WARNING: %s

%s An error occurred in the <MedicinesIndex> component. Consider adding an error boundary to your tree to customize error handling behavior.
Visit https://react.dev/link/error-boundaries to learn more about error boundaries. {"url":"http://127.0.0.1:8000/medicines","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T06:02:39.595Z"} 
[2025-08-26 06:04:53] local.ERROR: Unhandled Promise Rejection Error Page not found: ./pages/pos/index.tsx Error: Page not found: ./pages/pos/index.tsx
    at resolvePageComponent (http://[::1]:5173/node_modules/.vite/deps/laravel-vite-plugin_inertia-helpers.js?v=27b7766f:12:9)
    at resolve (http://[::1]:5173/resources/js/app.tsx:10:22)
    at CurrentPage.resolveComponent (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=aebf2b11:14350:54)
    at CurrentPage.resolve (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=aebf2b11:12072:33)
    at CurrentPage.set (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=aebf2b11:12002:17)
    at _Response.setPage (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=aebf2b11:13053:17)
    at async _Response.process (http://[::1]:5173/node_modules/.vite/deps/@inertiajs_react.js?v=aebf2b11:12980:5) {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T06:04:53.324Z"} 
[2025-08-26 06:05:31] local.DEBUG: [vite] server connection lost. Polling for restart... {"url":"http://127.0.0.1:8000/dashboard","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36","timestamp":"2025-08-26T06:05:30.950Z"} 
