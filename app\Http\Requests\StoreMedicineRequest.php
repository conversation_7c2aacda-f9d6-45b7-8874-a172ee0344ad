<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreMedicineRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'code' => ['required', 'string', 'max:50', 'unique:medicines,code'],
            'category_id' => ['required', 'exists:categories,id'],
            'supplier_id' => ['nullable', 'exists:suppliers,id'],
            'stock_quantity' => ['required', 'integer', 'min:0'],
            'purchase_price' => ['required', 'numeric', 'min:0'],
            'selling_price' => ['required', 'numeric', 'min:0'],
            // Optional fields that we'll ignore for now since they're not in the database
            'description' => ['nullable', 'string'],
            'min_stock_level' => ['nullable', 'integer', 'min:0'],
            'barcode' => ['nullable', 'string'],
            'batch_number' => ['nullable', 'string'],
            'expiry_date' => ['nullable', 'date'],
            'manufacturer' => ['nullable', 'string'],
        ];
    }

    /**
     * Get the validated data for database storage.
     */
    public function validatedForDatabase(): array
    {
        $validated = $this->validated();

        // Only keep fields that exist in the database
        return collect($validated)->only([
            'name',
            'code',
            'category_id',
            'supplier_id',
            'stock_quantity',
            'purchase_price',
            'selling_price',
        ])->toArray();
    }
}
