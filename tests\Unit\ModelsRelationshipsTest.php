<?php

namespace Tests\Unit;

use App\Models\Batch;
use App\Models\Category;
use App\Models\Medicine;
use App\Models\Supplier;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ModelsRelationshipsTest extends TestCase
{
    use RefreshDatabase;

    public function test_category_has_many_medicines(): void
    {
        $category = Category::factory()->has(Medicine::factory()->count(2))->create();

        $this->assertCount(2, $category->medicines);
    }

    public function test_supplier_has_many_medicines(): void
    {
        $supplier = Supplier::factory()->has(Medicine::factory()->count(3))->create();

        $this->assertCount(3, $supplier->medicines);
    }

    public function test_medicine_belongs_to_category_and_supplier(): void
    {
        $medicine = Medicine::factory()->create();

        $this->assertNotNull($medicine->category);
        $this->assertNotNull($medicine->supplier);
    }

    public function test_medicine_has_many_batches(): void
    {
        $medicine = Medicine::factory()->has(Batch::factory()->count(4))->create();

        $this->assertCount(4, $medicine->batches);
    }
}
