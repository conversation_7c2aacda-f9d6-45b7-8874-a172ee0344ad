<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\Medicine;
use App\Models\Supplier;
use App\Services\PurchaseService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PurchaseFlowTest extends TestCase
{
    use RefreshDatabase;

    public function test_purchase_increases_stock_and_creates_batches(): void
    {
        $category = Category::factory()->create();
        $supplier = Supplier::factory()->create();
        $medicine = Medicine::factory()->create([
            'category_id' => $category->id,
            'supplier_id' => $supplier->id,
            'stock_quantity' => 0,
        ]);

        $service = new PurchaseService(app('db'));
        $purchase = $service->createPurchase([
            'invoice_number' => 'INV-TEST-001',
            'purchased_at' => now()->toDateString(),
            'supplier_id' => $supplier->id,
            'items' => [
                ['medicine_id' => $medicine->id, 'quantity' => 15, 'unit_cost' => 3.5, 'batch_number' => 'BATCH-001', 'expiry_date' => now()->addMonths(4)->toDateString()],
                ['medicine_id' => $medicine->id, 'quantity' => 5, 'unit_cost' => 4.0, 'batch_number' => 'BATCH-002', 'expiry_date' => now()->addMonths(8)->toDateString()],
            ],
        ]);

        $this->assertEquals(20, $medicine->fresh()->stock_quantity);
        $this->assertCount(2, $purchase->fresh()->items);
        $this->assertDatabaseCount('batches', 2);
    }
}
