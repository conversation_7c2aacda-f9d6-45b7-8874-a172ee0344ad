<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\DB;
use Tests\TestCase;

class CategoryCrudTest extends TestCase
{
    use RefreshDatabase;

    public function test_index_requires_auth(): void
    {
        $this->get('/categories')->assertRedirect('/login');
    }

    public function test_admin_can_create_update_delete_category(): void
    {
        $this->actingAs(User::factory()->admin()->create());

        // Create
        $resp = $this->post('/categories', ['name' => 'Painkillers']);
        $resp->assertRedirect();

        // Update
        $id = DB::table('categories')->where('name', 'Painkillers')->value('id');
        $resp = $this->put("/categories/{$id}", ['name' => 'Analgesics']);
        $resp->assertRedirect();

        // Delete
        $resp = $this->delete("/categories/{$id}");
        $resp->assertRedirect();
    }

    public function test_cashier_cannot_create_update_delete_category(): void
    {
        $this->actingAs(User::factory()->cashier()->create());

        // Cannot create
        $resp = $this->post('/categories', ['name' => 'Painkillers']);
        $resp->assertForbidden();

        // Create a category as admin for testing update/delete
        $category = \App\Models\Category::factory()->create();

        // Cannot update
        $resp = $this->put("/categories/{$category->id}", ['name' => 'Updated']);
        $resp->assertForbidden();

        // Cannot delete
        $resp = $this->delete("/categories/{$category->id}");
        $resp->assertForbidden();
    }
}
