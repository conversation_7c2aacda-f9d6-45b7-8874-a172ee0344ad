import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import type { AlertFilters as AlertFiltersType } from '@/types/alerts';
import { router } from '@inertiajs/react';
import { useState } from 'react';

interface AlertFiltersProps {
    filters: AlertFiltersType;
}

export default function AlertFilters({ filters }: AlertFiltersProps) {
    const [lowStockThreshold, setLowStockThreshold] = useState(filters.low_stock_threshold);
    const [expiryDays, setExpiryDays] = useState(filters.expiry_days);

    const handleFilterUpdate = () => {
        router.get(
            window.location.pathname,
            {
                low_stock_threshold: lowStockThreshold,
                expiry_days: expiryDays,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Alert Settings</CardTitle>
                <CardDescription>Adjust thresholds for stock and expiry alerts</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="flex items-end space-x-4">
                    <div>
                        <Label htmlFor="low_stock_threshold">Low Stock Threshold</Label>
                        <Input
                            id="low_stock_threshold"
                            type="number"
                            min="1"
                            value={lowStockThreshold}
                            onChange={(e) => setLowStockThreshold(parseInt(e.target.value) || 10)}
                            className="w-32"
                        />
                    </div>
                    <div>
                        <Label htmlFor="expiry_days">Expiry Alert Days</Label>
                        <Input
                            id="expiry_days"
                            type="number"
                            min="1"
                            value={expiryDays}
                            onChange={(e) => setExpiryDays(parseInt(e.target.value) || 30)}
                            className="w-32"
                        />
                    </div>
                    <Button onClick={handleFilterUpdate}>Update Alerts</Button>
                </div>
            </CardContent>
        </Card>
    );
}
