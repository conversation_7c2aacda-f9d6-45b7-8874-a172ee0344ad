<?php

namespace App\Services;

use App\Models\Batch;
use App\Models\Medicine;
use App\Models\Purchase;
use App\Models\PurchaseItem;
use Illuminate\Database\DatabaseManager;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class PurchaseService
{
    public function __construct(public DatabaseManager $db) {}

    /**
     * Create a purchase from validated request data.
     */
    public function createPurchase(array $data): Purchase
    {
        return $this->db->transaction(function () use ($data) {
            $total = 0.0;

            $purchase = Purchase::query()->create([
                'invoice_number' => $data['invoice_number'],
                'purchased_at' => $data['purchased_at'],
                'supplier_id' => $data['supplier_id'],
                'total_cost' => 0,
            ]);

            foreach ($data['items'] as $item) {
                $medicine = Medicine::query()->findOrFail($item['medicine_id']);
                $qty = (int) $item['quantity'];
                $unit = (float) $item['unit_cost'];
                $batchNumber = $item['batch_number'];
                $expiry = $item['expiry_date'];

                $line = $qty * $unit;
                $total += $line;

                // Create batch for this purchase item
                $batch = Batch::query()->create([
                    'medicine_id' => $medicine->id,
                    'batch_number' => $batchNumber,
                    'expiry_date' => $expiry,
                    'quantity' => $qty,
                ]);

                PurchaseItem::query()->create([
                    'purchase_id' => $purchase->id,
                    'medicine_id' => $medicine->id,
                    'batch_id' => $batch->id,
                    'quantity' => $qty,
                    'unit_cost' => $unit,
                    'line_total' => $line,
                ]);

                $medicine->increment('stock_quantity', $qty);
            }

            $purchase->update(['total_cost' => $total]);

            return $purchase->load('items');
        });
    }
}
