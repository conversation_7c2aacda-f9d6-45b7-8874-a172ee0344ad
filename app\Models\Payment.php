<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payment extends Model
{
    /** @use HasFactory<\Database\Factories\PaymentFactory> */
    use HasFactory;

    protected $fillable = [
        'sale_id',
        'method',
        'amount',
    ];

    /** @return BelongsTo<Sale, self> */
    public function sale(): BelongsTo
    {
        return $this->belongsTo(Sale::class);
    }
}
