export interface Category {
    id: number;
    name: string;
}

export interface Supplier {
    id: number;
    name: string;
    contact_person?: string;
    phone?: string;
    email?: string;
    address?: string;
}

export interface Medicine {
    id: number;
    name: string;
    code: string;
    category_id: number;
    supplier_id: number;
    stock_quantity: number;
    purchase_price: number;
    selling_price: number;
    min_stock_level: number;
    description?: string;
    barcode?: string;
    batch_number?: string;
    expiry_date?: string;
    manufacturer?: string;
    created_at: string;
    updated_at: string;
    category?: Category;
    supplier?: Supplier;
    batches?: Batch[];
}

export interface Batch {
    id: number;
    medicine_id: number;
    batch_number: string;
    quantity: number;
    remaining_quantity: number;
    purchase_price: number;
    expiry_date: string;
    manufacturing_date?: string;
    created_at: string;
    updated_at: string;
    medicine?: Medicine;
}

export interface MedicineFilters {
    search?: string;
    category_id?: number;
    supplier_id?: number;
    stock_status?: 'all' | 'in_stock' | 'low_stock' | 'out_of_stock';
    sort_by?: 'name' | 'code' | 'stock_quantity' | 'selling_price' | 'created_at';
    sort_direction?: 'asc' | 'desc';
}

export interface PaginatedMedicines {
    data: Medicine[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    prev_page_url?: string;
    next_page_url?: string;
}

export interface MedicinesIndexProps {
    medicines: PaginatedMedicines;
    categories: Category[];
    suppliers: Supplier[];
    filters: MedicineFilters;
}
