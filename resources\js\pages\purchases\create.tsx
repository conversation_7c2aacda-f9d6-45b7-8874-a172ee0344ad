import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { index as purchasesIndex, store as purchasesStore } from '@/routes/purchases';
import { type BreadcrumbItem } from '@/types';
import { Head, router } from '@inertiajs/react';
import { Minus, Plus } from 'lucide-react';
import { useState } from 'react';

interface Supplier {
    id: number;
    name: string;
}

interface Medicine {
    id: number;
    name: string;
    code: string;
}

interface PurchaseItem {
    medicine_id: number;
    quantity: number;
    unit_cost: number;
    batch_number: string;
    expiry_date: string;
}

interface PurchasesCreateProps {
    suppliers: Supplier[];
    medicines: Medicine[];
}

export default function PurchasesCreate({ suppliers, medicines }: PurchasesCreateProps) {
    const [formData, setFormData] = useState({
        invoice_number: '',
        purchased_at: new Date().toISOString().split('T')[0],
        supplier_id: '',
    });

    const [items, setItems] = useState<PurchaseItem[]>([
        {
            medicine_id: 0,
            quantity: 1,
            unit_cost: 0,
            batch_number: '',
            expiry_date: '',
        },
    ]);

    const [errors, setErrors] = useState<Record<string, string>>({});

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Purchases', href: purchasesIndex().url },
        { title: 'New Purchase', href: '#' },
    ];

    const addItem = () => {
        setItems([
            ...items,
            {
                medicine_id: 0,
                quantity: 1,
                unit_cost: 0,
                batch_number: '',
                expiry_date: '',
            },
        ]);
    };

    const removeItem = (index: number) => {
        if (items.length > 1) {
            setItems(items.filter((_, i) => i !== index));
        }
    };

    const updateItem = (index: number, field: keyof PurchaseItem, value: any) => {
        const updatedItems = [...items];
        updatedItems[index] = { ...updatedItems[index], [field]: value };
        setItems(updatedItems);
    };

    const calculateTotal = () => {
        return items.reduce((total, item) => total + (item.quantity * item.unit_cost), 0);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setErrors({});

        const data = {
            ...formData,
            supplier_id: parseInt(formData.supplier_id),
            items: items.map(item => ({
                ...item,
                medicine_id: parseInt(item.medicine_id.toString()),
            })),
        };

        router.post(purchasesStore().url, data, {
            onError: (errors) => {
                setErrors(errors);
            },
        });
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="New Purchase" />
            
            <div className="space-y-6">
                <div>
                    <h1 className="text-2xl font-bold">Create Purchase Order</h1>
                    <p className="text-muted-foreground">
                        Create a new purchase order to receive stock from suppliers
                    </p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Purchase Details</CardTitle>
                            <CardDescription>
                                Enter the basic purchase order information
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <Label htmlFor="invoice_number">Invoice Number</Label>
                                    <Input
                                        id="invoice_number"
                                        value={formData.invoice_number}
                                        onChange={(e) => setFormData({ ...formData, invoice_number: e.target.value })}
                                        required
                                    />
                                    {errors.invoice_number && (
                                        <p className="text-sm text-red-600 mt-1">{errors.invoice_number}</p>
                                    )}
                                </div>
                                <div>
                                    <Label htmlFor="purchased_at">Purchase Date</Label>
                                    <Input
                                        id="purchased_at"
                                        type="date"
                                        value={formData.purchased_at}
                                        onChange={(e) => setFormData({ ...formData, purchased_at: e.target.value })}
                                        required
                                    />
                                    {errors.purchased_at && (
                                        <p className="text-sm text-red-600 mt-1">{errors.purchased_at}</p>
                                    )}
                                </div>
                                <div>
                                    <Label htmlFor="supplier_id">Supplier</Label>
                                    <Select
                                        value={formData.supplier_id}
                                        onValueChange={(value) => setFormData({ ...formData, supplier_id: value })}
                                        required
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select supplier" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {suppliers.map((supplier) => (
                                                <SelectItem key={supplier.id} value={supplier.id.toString()}>
                                                    {supplier.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.supplier_id && (
                                        <p className="text-sm text-red-600 mt-1">{errors.supplier_id}</p>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle>Purchase Items</CardTitle>
                                    <CardDescription>
                                        Add medicines and their batch information
                                    </CardDescription>
                                </div>
                                <Button type="button" onClick={addItem} variant="outline">
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add Item
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Medicine</TableHead>
                                        <TableHead>Quantity</TableHead>
                                        <TableHead>Unit Cost</TableHead>
                                        <TableHead>Batch Number</TableHead>
                                        <TableHead>Expiry Date</TableHead>
                                        <TableHead>Line Total</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {items.map((item, index) => (
                                        <TableRow key={index}>
                                            <TableCell>
                                                <Select
                                                    value={item.medicine_id.toString()}
                                                    onValueChange={(value) => updateItem(index, 'medicine_id', parseInt(value))}
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select medicine" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {medicines.map((medicine) => (
                                                            <SelectItem key={medicine.id} value={medicine.id.toString()}>
                                                                {medicine.name} ({medicine.code})
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </TableCell>
                                            <TableCell>
                                                <Input
                                                    type="number"
                                                    min="1"
                                                    value={item.quantity}
                                                    onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value) || 0)}
                                                    className="w-20"
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <Input
                                                    type="number"
                                                    min="0"
                                                    step="0.01"
                                                    value={item.unit_cost}
                                                    onChange={(e) => updateItem(index, 'unit_cost', parseFloat(e.target.value) || 0)}
                                                    className="w-24"
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <Input
                                                    value={item.batch_number}
                                                    onChange={(e) => updateItem(index, 'batch_number', e.target.value)}
                                                    className="w-32"
                                                />
                                            </TableCell>
                                            <TableCell>
                                                <Input
                                                    type="date"
                                                    value={item.expiry_date}
                                                    onChange={(e) => updateItem(index, 'expiry_date', e.target.value)}
                                                    className="w-36"
                                                />
                                            </TableCell>
                                            <TableCell className="font-medium">
                                                {formatCurrency(item.quantity * item.unit_cost)}
                                            </TableCell>
                                            <TableCell>
                                                <Button
                                                    type="button"
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => removeItem(index)}
                                                    disabled={items.length === 1}
                                                >
                                                    <Minus className="h-4 w-4" />
                                                </Button>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>

                            <div className="mt-4 flex justify-end">
                                <div className="text-right">
                                    <p className="text-lg font-semibold">
                                        Total: {formatCurrency(calculateTotal())}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <div className="flex justify-end space-x-2">
                        <Button type="button" variant="outline" onClick={() => router.visit(purchasesIndex().url)}>
                            Cancel
                        </Button>
                        <Button type="submit">
                            Create Purchase Order
                        </Button>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
