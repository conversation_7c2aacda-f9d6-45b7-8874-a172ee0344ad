'use client';

import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import type { Category, Medicine, Supplier } from '@/types/medicines';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from '@inertiajs/react';
import { Loader2, Package, Save, X } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const medicineSchema = z.object({
    name: z.string().min(1, 'Medicine name is required').max(255, 'Name is too long'),
    code: z.string().min(1, 'Medicine code is required').max(50, 'Code is too long'),
    description: z.string().optional(),
    category_id: z.number().min(1, 'Please select a category'),
    supplier_id: z.number().min(1, 'Please select a supplier'),
    purchase_price: z.number().min(0, 'Purchase price must be positive'),
    selling_price: z.number().min(0, 'Selling price must be positive'),
    stock_quantity: z.number().min(0, 'Stock quantity must be positive'),
    min_stock_level: z.number().min(0, 'Minimum stock level must be positive'),
    barcode: z.string().optional(),
    batch_number: z.string().optional(),
    expiry_date: z.string().optional(),
    manufacturer: z.string().optional(),
});

type MedicineFormData = z.infer<typeof medicineSchema>;

interface MedicineFormProps {
    medicine?: Medicine | null;
    categories: Category[];
    suppliers: Supplier[];
    onCancel?: () => void;
    className?: string;
}

export function MedicineForm({ medicine, categories, suppliers, onCancel, className }: MedicineFormProps) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const isEditing = !!medicine;

    const form = useForm<MedicineFormData>({
        resolver: zodResolver(medicineSchema),
        defaultValues: {
            name: medicine?.name || '',
            code: medicine?.code || '',
            description: medicine?.description || '',
            category_id: medicine?.category_id || categories[0]?.id || 1,
            supplier_id: medicine?.supplier_id || suppliers[0]?.id || 1,
            purchase_price: medicine?.purchase_price || 0,
            selling_price: medicine?.selling_price || 0,
            stock_quantity: medicine?.stock_quantity || 0,
            min_stock_level: medicine?.min_stock_level || 10,
            barcode: medicine?.barcode || '',
            batch_number: medicine?.batch_number || '',
            expiry_date: medicine?.expiry_date || '',
            manufacturer: medicine?.manufacturer || '',
        },
    });

    const generateCode = () => {
        const name = form.getValues('name');
        if (name) {
            const code = name
                .replace(/[^a-zA-Z0-9\s]/g, '') // Remove special characters
                .replace(/\s+/g, '-') // Replace spaces with hyphens
                .toUpperCase()
                .slice(0, 20); // Limit to 20 characters
            form.setValue('code', code);
        }
    };

    const calculateMargin = () => {
        const purchasePrice = form.getValues('purchase_price');
        const sellingPrice = form.getValues('selling_price');
        if (purchasePrice > 0 && sellingPrice > 0) {
            const margin = ((sellingPrice - purchasePrice) / purchasePrice) * 100;
            return margin.toFixed(1);
        }
        return '0';
    };

    const onSubmit = async (data: MedicineFormData) => {
        setIsSubmitting(true);

        try {
            if (isEditing) {
                router.put(`/medicines/${medicine.id}`, data, {
                    onSuccess: () => {
                        // Success will be handled by the page redirect
                    },
                    onError: (errors) => {
                        console.error('Validation errors:', errors);
                        // Set server validation errors on form
                        Object.entries(errors).forEach(([key, message]) => {
                            if (key in form.formState.errors) {
                                form.setError(key as keyof MedicineFormData, {
                                    type: 'server',
                                    message: Array.isArray(message) ? message[0] : (message as string),
                                });
                            }
                        });
                    },
                    onFinish: () => setIsSubmitting(false),
                });
            } else {
                router.post('/medicines', data, {
                    onSuccess: () => {
                        // Success will be handled by the page redirect
                    },
                    onError: (errors) => {
                        console.error('Validation errors:', errors);
                        // Set server validation errors on form
                        Object.entries(errors).forEach(([key, message]) => {
                            if (key in form.formState.errors) {
                                form.setError(key as keyof MedicineFormData, {
                                    type: 'server',
                                    message: Array.isArray(message) ? message[0] : (message as string),
                                });
                            }
                        });
                    },
                    onFinish: () => setIsSubmitting(false),
                });
            }
        } catch (error) {
            console.error('Form submission error:', error);
            setIsSubmitting(false);
        }
    };

    return (
        <Card className={className}>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    {isEditing ? 'Edit Medicine' : 'Add New Medicine'}
                </CardTitle>
            </CardHeader>
            <CardContent>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        {/* Basic Information */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Basic Information</h3>

                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <FormField
                                    control={form.control}
                                    name="name"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Medicine Name *</FormLabel>
                                            <FormControl>
                                                <Input
                                                    placeholder="Enter medicine name"
                                                    {...field}
                                                    onBlur={() => {
                                                        field.onBlur();
                                                        if (!form.getValues('code')) {
                                                            generateCode();
                                                        }
                                                    }}
                                                />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="code"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Medicine Code *</FormLabel>
                                            <div className="flex gap-2">
                                                <FormControl>
                                                    <Input placeholder="Enter or generate code" {...field} />
                                                </FormControl>
                                                <Button type="button" variant="outline" size="sm" onClick={generateCode}>
                                                    Generate
                                                </Button>
                                            </div>
                                            <FormDescription>Unique identifier for this medicine</FormDescription>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>

                            <FormField
                                control={form.control}
                                name="description"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Description</FormLabel>
                                        <FormControl>
                                            <Textarea placeholder="Enter medicine description, uses, dosage info..." rows={3} {...field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>

                        <Separator />

                        {/* Category and Supplier */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Classification</h3>

                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <FormField
                                    control={form.control}
                                    name="category_id"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Category *</FormLabel>
                                            <Select onValueChange={(value) => field.onChange(parseInt(value))} value={field.value?.toString()}>
                                                <FormControl>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select category" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {categories.map((category) => (
                                                        <SelectItem key={category.id} value={category.id.toString()}>
                                                            {category.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="supplier_id"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Supplier *</FormLabel>
                                            <Select onValueChange={(value) => field.onChange(parseInt(value))} value={field.value?.toString()}>
                                                <FormControl>
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Select supplier" />
                                                    </SelectTrigger>
                                                </FormControl>
                                                <SelectContent>
                                                    {suppliers.map((supplier) => (
                                                        <SelectItem key={supplier.id} value={supplier.id.toString()}>
                                                            {supplier.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </div>

                        <Separator />

                        {/* Pricing */}
                        <div className="space-y-4">
                            <div className="flex items-center justify-between">
                                <h3 className="text-lg font-medium">Pricing</h3>
                                <Badge variant="outline">Margin: {calculateMargin()}%</Badge>
                            </div>

                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <FormField
                                    control={form.control}
                                    name="purchase_price"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Purchase Price *</FormLabel>
                                            <FormControl>
                                                <Input
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    placeholder="0.00"
                                                    {...field}
                                                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                                />
                                            </FormControl>
                                            <FormDescription>Cost price from supplier</FormDescription>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="selling_price"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Selling Price *</FormLabel>
                                            <FormControl>
                                                <Input
                                                    type="number"
                                                    step="0.01"
                                                    min="0"
                                                    placeholder="0.00"
                                                    {...field}
                                                    onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                                />
                                            </FormControl>
                                            <FormDescription>Price charged to customers</FormDescription>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </div>

                        <Separator />

                        {/* Stock Information */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Stock Information</h3>

                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <FormField
                                    control={form.control}
                                    name="stock_quantity"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Current Stock *</FormLabel>
                                            <FormControl>
                                                <Input
                                                    type="number"
                                                    min="0"
                                                    placeholder="0"
                                                    {...field}
                                                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                                                />
                                            </FormControl>
                                            <FormDescription>Current quantity in stock</FormDescription>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="min_stock_level"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Minimum Stock Level *</FormLabel>
                                            <FormControl>
                                                <Input
                                                    type="number"
                                                    min="0"
                                                    placeholder="10"
                                                    {...field}
                                                    onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                                                />
                                            </FormControl>
                                            <FormDescription>Reorder when stock falls below this level</FormDescription>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </div>

                        <Separator />

                        {/* Additional Information */}
                        <div className="space-y-4">
                            <h3 className="text-lg font-medium">Additional Information</h3>

                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                <FormField
                                    control={form.control}
                                    name="barcode"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Barcode</FormLabel>
                                            <FormControl>
                                                <Input placeholder="Enter barcode" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="manufacturer"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Manufacturer</FormLabel>
                                            <FormControl>
                                                <Input placeholder="Enter manufacturer name" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="batch_number"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Batch Number</FormLabel>
                                            <FormControl>
                                                <Input placeholder="Enter batch number" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="expiry_date"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Expiry Date</FormLabel>
                                            <FormControl>
                                                <Input type="date" {...field} />
                                            </FormControl>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />
                            </div>
                        </div>

                        {/* Form Actions */}
                        <div className="flex flex-col gap-3 pt-4 sm:flex-row">
                            <Button type="submit" disabled={isSubmitting} className="flex-1 sm:flex-none">
                                {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
                                {isEditing ? 'Update Medicine' : 'Create Medicine'}
                            </Button>

                            {onCancel && (
                                <Button type="button" variant="outline" onClick={onCancel} disabled={isSubmitting} className="flex-1 sm:flex-none">
                                    <X className="mr-2 h-4 w-4" />
                                    Cancel
                                </Button>
                            )}
                        </div>
                    </form>
                </Form>
            </CardContent>
        </Card>
    );
}
