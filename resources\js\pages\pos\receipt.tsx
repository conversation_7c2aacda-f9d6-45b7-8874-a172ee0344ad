import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { format } from 'date-fns';
import { ArrowLeft, Printer } from 'lucide-react';

interface Medicine {
    id: number;
    name: string;
    code: string;
}

interface SaleItem {
    id: number;
    quantity: number;
    unit_price: number;
    line_total: number;
    medicine: Medicine;
}

interface Payment {
    id: number;
    method: string;
    amount: number;
}

interface Cashier {
    id: number;
    name: string;
}

interface Sale {
    id: number;
    subtotal: number;
    discount_type: string | null;
    discount_value: number;
    total: number;
    sold_at: string;
    items: SaleItem[];
    payments: Payment[];
    cashier: Cashier;
    created_at: string;
}

interface ReceiptProps {
    sale: Sale;
}

export default function Receipt({ sale }: ReceiptProps) {
    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'POS', href: '/pos' },
        { title: 'Receipt', href: '#' },
    ];

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    const handlePrint = () => {
        window.print();
    };

    const calculateDiscount = () => {
        if (sale.discount_type === 'percent') {
            return (sale.subtotal * sale.discount_value) / 100;
        } else if (sale.discount_type === 'fixed') {
            return sale.discount_value;
        }
        return 0;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Receipt #${sale.id}`} />
            
            <div className="max-w-2xl mx-auto space-y-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link href="/pos">
                            <Button variant="outline">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to POS
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-2xl font-bold">Sale Receipt</h1>
                            <p className="text-muted-foreground">
                                Sale #{sale.id} • {format(new Date(sale.sold_at), 'MMM dd, yyyy HH:mm')}
                            </p>
                        </div>
                    </div>
                    
                    <Button onClick={handlePrint}>
                        <Printer className="mr-2 h-4 w-4" />
                        Print Receipt
                    </Button>
                </div>

                <Card className="print:shadow-none">
                    <CardHeader className="text-center border-b">
                        <CardTitle className="text-xl">Pharmacy Management System</CardTitle>
                        <CardDescription>
                            Sale Receipt #{sale.id}
                        </CardDescription>
                        <div className="text-sm text-muted-foreground">
                            <p>Date: {format(new Date(sale.sold_at), 'MMMM dd, yyyy')}</p>
                            <p>Time: {format(new Date(sale.sold_at), 'HH:mm:ss')}</p>
                            <p>Cashier: {sale.cashier.name}</p>
                        </div>
                    </CardHeader>
                    
                    <CardContent className="p-6">
                        <div className="space-y-4">
                            <div>
                                <h3 className="font-semibold mb-3">Items Purchased</h3>
                                <div className="space-y-2">
                                    {sale.items.map((item) => (
                                        <div key={item.id} className="flex justify-between items-start">
                                            <div className="flex-1">
                                                <p className="font-medium">{item.medicine.name}</p>
                                                <p className="text-sm text-muted-foreground">
                                                    {item.medicine.code} • Qty: {item.quantity} × {formatCurrency(item.unit_price)}
                                                </p>
                                            </div>
                                            <p className="font-medium">{formatCurrency(item.line_total)}</p>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            <div className="border-t pt-4 space-y-2">
                                <div className="flex justify-between">
                                    <span>Subtotal:</span>
                                    <span>{formatCurrency(sale.subtotal)}</span>
                                </div>
                                
                                {calculateDiscount() > 0 && (
                                    <div className="flex justify-between text-red-600">
                                        <span>
                                            Discount ({sale.discount_type === 'percent' ? `${sale.discount_value}%` : 'Fixed'}):
                                        </span>
                                        <span>-{formatCurrency(calculateDiscount())}</span>
                                    </div>
                                )}
                                
                                <div className="flex justify-between font-bold text-lg border-t pt-2">
                                    <span>Total:</span>
                                    <span>{formatCurrency(sale.total)}</span>
                                </div>
                            </div>

                            <div className="border-t pt-4">
                                <h3 className="font-semibold mb-3">Payment Details</h3>
                                <div className="space-y-2">
                                    {sale.payments.map((payment) => (
                                        <div key={payment.id} className="flex justify-between">
                                            <span className="capitalize">{payment.method}:</span>
                                            <span>{formatCurrency(payment.amount)}</span>
                                        </div>
                                    ))}
                                    
                                    {sale.payments.reduce((sum, p) => sum + p.amount, 0) > sale.total && (
                                        <div className="flex justify-between text-green-600 font-medium">
                                            <span>Change:</span>
                                            <span>
                                                {formatCurrency(
                                                    sale.payments.reduce((sum, p) => sum + p.amount, 0) - sale.total
                                                )}
                                            </span>
                                        </div>
                                    )}
                                </div>
                            </div>

                            <div className="border-t pt-4 text-center text-sm text-muted-foreground">
                                <p>Thank you for your purchase!</p>
                                <p>Please keep this receipt for your records.</p>
                                <p className="mt-2">
                                    Generated on {format(new Date(), 'MMM dd, yyyy HH:mm:ss')}
                                </p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
