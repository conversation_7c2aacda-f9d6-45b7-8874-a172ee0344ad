import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { create as purchasesCreate, destroy as purchasesDestroy, show as purchasesShow } from '@/routes/purchases';
import { type BreadcrumbItem, type SharedData } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { format } from 'date-fns';
import { Eye, Plus, Search, Trash2 } from 'lucide-react';
import { useState } from 'react';

interface Supplier {
    id: number;
    name: string;
}

interface Purchase {
    id: number;
    invoice_number: string;
    purchased_at: string;
    total_cost: number;
    supplier: Supplier;
    created_at: string;
}

interface PurchasesIndexProps {
    purchases: {
        data: Purchase[];
        links: any[];
        meta: any;
    };
    filters: {
        search: string;
    };
}

export default function PurchasesIndex({ purchases, filters }: PurchasesIndexProps) {
    const { auth } = usePage<SharedData>().props;
    const isAdmin = auth.user?.role === 'admin';
    
    const [search, setSearch] = useState(filters.search);

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Purchases', href: '#' },
    ];

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        router.get(window.location.pathname, { search }, {
            preserveState: true,
            replace: true,
        });
    };

    const handleDelete = (purchase: Purchase) => {
        if (confirm(`Are you sure you want to delete purchase ${purchase.invoice_number}?`)) {
            router.delete(purchasesDestroy(purchase.id).url);
        }
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Purchases" />
            
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold">Purchase Management</h1>
                        <p className="text-muted-foreground">
                            Manage purchase orders and stock receipts
                        </p>
                    </div>
                    
                    {isAdmin && (
                        <Link href={purchasesCreate().url}>
                            <Button>
                                <Plus className="mr-2 h-4 w-4" />
                                New Purchase
                            </Button>
                        </Link>
                    )}
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Purchase Orders</CardTitle>
                        <CardDescription>
                            View and manage all purchase orders
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="mb-4">
                            <form onSubmit={handleSearch} className="flex gap-2">
                                <div className="relative flex-1">
                                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        placeholder="Search by invoice number or supplier..."
                                        value={search}
                                        onChange={(e) => setSearch(e.target.value)}
                                        className="pl-8"
                                    />
                                </div>
                                <Button type="submit" variant="outline">
                                    Search
                                </Button>
                            </form>
                        </div>

                        {purchases.data.length === 0 ? (
                            <div className="text-center py-8">
                                <p className="text-muted-foreground">No purchases found.</p>
                                {isAdmin && (
                                    <Link href={purchasesCreate().url}>
                                        <Button className="mt-4">
                                            <Plus className="mr-2 h-4 w-4" />
                                            Create First Purchase
                                        </Button>
                                    </Link>
                                )}
                            </div>
                        ) : (
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Invoice #</TableHead>
                                        <TableHead>Supplier</TableHead>
                                        <TableHead>Purchase Date</TableHead>
                                        <TableHead>Total Cost</TableHead>
                                        <TableHead>Created</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {purchases.data.map((purchase) => (
                                        <TableRow key={purchase.id}>
                                            <TableCell className="font-medium">
                                                {purchase.invoice_number}
                                            </TableCell>
                                            <TableCell>{purchase.supplier.name}</TableCell>
                                            <TableCell>
                                                {format(new Date(purchase.purchased_at), 'MMM dd, yyyy')}
                                            </TableCell>
                                            <TableCell className="font-medium">
                                                {formatCurrency(purchase.total_cost)}
                                            </TableCell>
                                            <TableCell>
                                                {format(new Date(purchase.created_at), 'MMM dd, yyyy')}
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex space-x-2">
                                                    <Link href={purchasesShow(purchase.id).url}>
                                                        <Button size="sm" variant="outline">
                                                            <Eye className="h-4 w-4" />
                                                        </Button>
                                                    </Link>
                                                    {isAdmin && (
                                                        <Button
                                                            size="sm"
                                                            variant="outline"
                                                            onClick={() => handleDelete(purchase)}
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    )}
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
