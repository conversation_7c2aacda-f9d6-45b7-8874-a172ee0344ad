'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import type { Category, Supplier } from '@/types/medicines';
import { AlertTriangle, CheckCircle, Filter, Package, Search, TrendingUp, X } from 'lucide-react';
import { useEffect, useState } from 'react';

export interface MedicineFilters {
    search: string;
    category_id?: number | null;
    supplier_id?: number | null;
    stock_status?: 'all' | 'in_stock' | 'low_stock' | 'out_of_stock';
    min_price?: number;
    max_price?: number;
}

interface MedicineFiltersComponentProps {
    filters: MedicineFilters;
    onFiltersChange: (filters: MedicineFilters) => void;
    categories: Category[];
    suppliers: Supplier[];
    className?: string;
}

export function MedicineFiltersComponent({ filters, onFiltersChange, categories, suppliers, className }: MedicineFiltersComponentProps) {
    const [localFilters, setLocalFilters] = useState<MedicineFilters>(filters);
    const [showAdvanced, setShowAdvanced] = useState(false);

    // Apply filters with debounce for search
    useEffect(() => {
        const timeoutId = setTimeout(
            () => {
                onFiltersChange(localFilters);
            },
            localFilters.search !== filters.search ? 300 : 0,
        );

        return () => clearTimeout(timeoutId);
    }, [localFilters, onFiltersChange, filters.search]);

    const updateFilter = (key: keyof MedicineFilters, value: any) => {
        setLocalFilters((prev) => ({
            ...prev,
            [key]: value,
        }));
    };

    const clearFilters = () => {
        const clearedFilters: MedicineFilters = {
            search: '',
            category_id: null,
            supplier_id: null,
            stock_status: 'all',
            min_price: undefined,
            max_price: undefined,
        };
        setLocalFilters(clearedFilters);
        onFiltersChange(clearedFilters);
    };

    const hasActiveFilters =
        localFilters.search ||
        localFilters.category_id ||
        localFilters.supplier_id ||
        (localFilters.stock_status && localFilters.stock_status !== 'all') ||
        localFilters.min_price ||
        localFilters.max_price;

    const getActiveFilterCount = () => {
        let count = 0;
        if (localFilters.search) count++;
        if (localFilters.category_id) count++;
        if (localFilters.supplier_id) count++;
        if (localFilters.stock_status && localFilters.stock_status !== 'all') count++;
        if (localFilters.min_price) count++;
        if (localFilters.max_price) count++;
        return count;
    };

    const stockStatusOptions = [
        { value: 'all', label: 'All Stock Levels', icon: Package },
        { value: 'in_stock', label: 'In Stock', icon: CheckCircle },
        { value: 'low_stock', label: 'Low Stock', icon: TrendingUp },
        { value: 'out_of_stock', label: 'Out of Stock', icon: AlertTriangle },
    ];

    return (
        <Card className={className}>
            <CardContent className="p-4">
                <div className="space-y-4">
                    {/* Search and quick filters */}
                    <div className="flex flex-col gap-4 sm:flex-row">
                        <div className="relative flex-1">
                            <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-muted-foreground" />
                            <Input
                                placeholder="Search medicines by name, code, or description..."
                                value={localFilters.search}
                                onChange={(e) => updateFilter('search', e.target.value)}
                                className="pl-10"
                            />
                        </div>

                        <div className="flex gap-2">
                            <Button variant="outline" size="sm" onClick={() => setShowAdvanced(!showAdvanced)} className="gap-2">
                                <Filter className="h-4 w-4" />
                                Filters
                                {hasActiveFilters && (
                                    <Badge variant="secondary" className="ml-1 h-5 w-5 rounded-full p-0 text-xs">
                                        {getActiveFilterCount()}
                                    </Badge>
                                )}
                            </Button>

                            {hasActiveFilters && (
                                <Button variant="ghost" size="sm" onClick={clearFilters} className="gap-2">
                                    <X className="h-4 w-4" />
                                    Clear
                                </Button>
                            )}
                        </div>
                    </div>

                    {/* Advanced filters */}
                    {showAdvanced && (
                        <>
                            <Separator />
                            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
                                {/* Category filter */}
                                <div className="space-y-2">
                                    <label className="text-sm font-medium">Category</label>
                                    <Select
                                        value={localFilters.category_id?.toString() || 'all'}
                                        onValueChange={(value) => updateFilter('category_id', value === 'all' ? null : parseInt(value))}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="All categories" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Categories</SelectItem>
                                            {categories.map((category) => (
                                                <SelectItem key={category.id} value={category.id.toString()}>
                                                    {category.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Supplier filter */}
                                <div className="space-y-2">
                                    <label className="text-sm font-medium">Supplier</label>
                                    <Select
                                        value={localFilters.supplier_id?.toString() || 'all'}
                                        onValueChange={(value) => updateFilter('supplier_id', value === 'all' ? null : parseInt(value))}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="All suppliers" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="all">All Suppliers</SelectItem>
                                            {suppliers.map((supplier) => (
                                                <SelectItem key={supplier.id} value={supplier.id.toString()}>
                                                    {supplier.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Stock status filter */}
                                <div className="space-y-2">
                                    <label className="text-sm font-medium">Stock Status</label>
                                    <Select
                                        value={localFilters.stock_status || 'all'}
                                        onValueChange={(value) => updateFilter('stock_status', value as MedicineFilters['stock_status'])}
                                    >
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {stockStatusOptions.map((option) => {
                                                const Icon = option.icon;
                                                return (
                                                    <SelectItem key={option.value} value={option.value}>
                                                        <div className="flex items-center gap-2">
                                                            <Icon className="h-4 w-4" />
                                                            {option.label}
                                                        </div>
                                                    </SelectItem>
                                                );
                                            })}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {/* Price range */}
                                <div className="space-y-2">
                                    <label className="text-sm font-medium">Price Range</label>
                                    <div className="flex gap-2">
                                        <Input
                                            type="number"
                                            placeholder="Min"
                                            value={localFilters.min_price || ''}
                                            onChange={(e) => updateFilter('min_price', e.target.value ? parseFloat(e.target.value) : undefined)}
                                            min="0"
                                            step="0.01"
                                        />
                                        <Input
                                            type="number"
                                            placeholder="Max"
                                            value={localFilters.max_price || ''}
                                            onChange={(e) => updateFilter('max_price', e.target.value ? parseFloat(e.target.value) : undefined)}
                                            min="0"
                                            step="0.01"
                                        />
                                    </div>
                                </div>
                            </div>
                        </>
                    )}

                    {/* Active filters display */}
                    {hasActiveFilters && (
                        <>
                            <Separator />
                            <div className="flex flex-wrap gap-2">
                                <span className="text-sm text-muted-foreground">Active filters:</span>

                                {localFilters.search && (
                                    <Badge variant="secondary" className="gap-1">
                                        Search: "{localFilters.search}"
                                        <X className="h-3 w-3 cursor-pointer" onClick={() => updateFilter('search', '')} />
                                    </Badge>
                                )}

                                {localFilters.category_id && (
                                    <Badge variant="secondary" className="gap-1">
                                        Category: {categories.find((c) => c.id === localFilters.category_id)?.name}
                                        <X className="h-3 w-3 cursor-pointer" onClick={() => updateFilter('category_id', null)} />
                                    </Badge>
                                )}

                                {localFilters.supplier_id && (
                                    <Badge variant="secondary" className="gap-1">
                                        Supplier: {suppliers.find((s) => s.id === localFilters.supplier_id)?.name}
                                        <X className="h-3 w-3 cursor-pointer" onClick={() => updateFilter('supplier_id', null)} />
                                    </Badge>
                                )}

                                {localFilters.stock_status && localFilters.stock_status !== 'all' && (
                                    <Badge variant="secondary" className="gap-1">
                                        Status: {stockStatusOptions.find((o) => o.value === localFilters.stock_status)?.label}
                                        <X className="h-3 w-3 cursor-pointer" onClick={() => updateFilter('stock_status', 'all')} />
                                    </Badge>
                                )}

                                {(localFilters.min_price || localFilters.max_price) && (
                                    <Badge variant="secondary" className="gap-1">
                                        Price: ${localFilters.min_price || '0'} - ${localFilters.max_price || '∞'}
                                        <X
                                            className="h-3 w-3 cursor-pointer"
                                            onClick={() => {
                                                updateFilter('min_price', undefined);
                                                updateFilter('max_price', undefined);
                                            }}
                                        />
                                    </Badge>
                                )}
                            </div>
                        </>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}
