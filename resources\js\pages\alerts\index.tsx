import { AlertFilters, AlertsEmptyState, AlertSummaryCards, ExpiryAlertTable, StockAlertTable } from '@/components/alerts';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import type { AlertsData } from '@/types/alerts';
import { Head } from '@inertiajs/react';

interface AlertsIndexProps extends AlertsData {}

export default function AlertsIndex({
    summary,
    low_stock_medicines,
    out_of_stock_medicines,
    expiring_soon_batches,
    expired_batches,
    filters,
}: AlertsIndexProps) {
    const breadcrumbs: BreadcrumbItem[] = [{ title: 'Alerts', href: '#' }];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Inventory Alerts" />
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold">Inventory Alerts</h1>
                        <p className="text-muted-foreground">Monitor stock levels and expiry dates</p>
                    </div>
                </div>

                {/* Alert Summary Cards */}
                <AlertSummaryCards summary={summary} />

                {/* Filters */}
                <AlertFilters filters={filters} />

                {/* Out of Stock Medicines */}
                <StockAlertTable
                    title="Out of Stock Medicines"
                    description="Medicines that are completely out of stock"
                    medicines={out_of_stock_medicines}
                    alertType="out_of_stock"
                />

                {/* Low Stock Medicines */}
                <StockAlertTable
                    title="Low Stock Medicines"
                    description="Medicines with stock below threshold"
                    medicines={low_stock_medicines}
                    alertType="low_stock"
                    threshold={filters.low_stock_threshold}
                />

                {/* Expired Batches */}
                <ExpiryAlertTable
                    title="Expired Batches"
                    description="Batches that have already expired"
                    batches={expired_batches}
                    alertType="expired"
                />

                {/* Expiring Soon Batches */}
                <ExpiryAlertTable
                    title="Expiring Soon Batches"
                    description="Batches expiring within"
                    batches={expiring_soon_batches}
                    alertType="expiring"
                    expiryDays={filters.expiry_days}
                />

                {/* No Alerts Message */}
                {summary.total_alerts === 0 && <AlertsEmptyState />}
            </div>
        </AppLayout>
    );
}
