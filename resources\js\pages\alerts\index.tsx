import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router } from '@inertiajs/react';
import { format } from 'date-fns';
import { AlertTriangle, Package, PackageX, Timer, XCircle } from 'lucide-react';
import { useState } from 'react';

interface Category {
    id: number;
    name: string;
}

interface Supplier {
    id: number;
    name: string;
}

interface Medicine {
    id: number;
    name: string;
    code: string;
    stock_quantity: number;
    category: Category;
    supplier: Supplier;
}

interface Batch {
    id: number;
    batch_number: string;
    expiry_date: string;
    quantity: number;
    medicine: {
        id: number;
        name: string;
        code: string;
        category: Category;
    };
}

interface AlertsSummary {
    low_stock_count: number;
    out_of_stock_count: number;
    expiring_soon_count: number;
    expired_count: number;
    total_alerts: number;
}

interface AlertsIndexProps {
    summary: AlertsSummary;
    low_stock_medicines: Medicine[];
    out_of_stock_medicines: Medicine[];
    expiring_soon_batches: Batch[];
    expired_batches: Batch[];
    filters: {
        low_stock_threshold: number;
        expiry_days: number;
    };
}

export default function AlertsIndex({
    summary,
    low_stock_medicines,
    out_of_stock_medicines,
    expiring_soon_batches,
    expired_batches,
    filters,
}: AlertsIndexProps) {
    const [lowStockThreshold, setLowStockThreshold] = useState(filters.low_stock_threshold);
    const [expiryDays, setExpiryDays] = useState(filters.expiry_days);

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Alerts', href: '#' },
    ];

    const handleFilterUpdate = () => {
        router.get(window.location.pathname, {
            low_stock_threshold: lowStockThreshold,
            expiry_days: expiryDays,
        }, {
            preserveState: true,
            replace: true,
        });
    };

    const getAlertIcon = (type: string) => {
        switch (type) {
            case 'out_of_stock':
                return <PackageX className="h-5 w-5 text-red-500" />;
            case 'low_stock':
                return <Package className="h-5 w-5 text-yellow-500" />;
            case 'expired':
                return <XCircle className="h-5 w-5 text-red-500" />;
            case 'expiring':
                return <Timer className="h-5 w-5 text-orange-500" />;
            default:
                return <AlertTriangle className="h-5 w-5 text-gray-500" />;
        }
    };

    const getDaysUntilExpiry = (expiryDate: string) => {
        const expiry = new Date(expiryDate);
        const today = new Date();
        const diffTime = expiry.getTime() - today.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays;
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Inventory Alerts" />
            
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold">Inventory Alerts</h1>
                        <p className="text-muted-foreground">
                            Monitor stock levels and expiry dates
                        </p>
                    </div>
                </div>

                {/* Alert Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <AlertTriangle className="h-5 w-5 text-blue-500" />
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Alerts</p>
                                    <p className="text-2xl font-bold">{summary.total_alerts}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <PackageX className="h-5 w-5 text-red-500" />
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Out of Stock</p>
                                    <p className="text-2xl font-bold text-red-600">{summary.out_of_stock_count}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <Package className="h-5 w-5 text-yellow-500" />
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Low Stock</p>
                                    <p className="text-2xl font-bold text-yellow-600">{summary.low_stock_count}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <XCircle className="h-5 w-5 text-red-500" />
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Expired</p>
                                    <p className="text-2xl font-bold text-red-600">{summary.expired_count}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <Timer className="h-5 w-5 text-orange-500" />
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Expiring Soon</p>
                                    <p className="text-2xl font-bold text-orange-600">{summary.expiring_soon_count}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>Alert Settings</CardTitle>
                        <CardDescription>
                            Adjust thresholds for stock and expiry alerts
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-end space-x-4">
                            <div>
                                <Label htmlFor="low_stock_threshold">Low Stock Threshold</Label>
                                <Input
                                    id="low_stock_threshold"
                                    type="number"
                                    min="1"
                                    value={lowStockThreshold}
                                    onChange={(e) => setLowStockThreshold(parseInt(e.target.value) || 10)}
                                    className="w-32"
                                />
                            </div>
                            <div>
                                <Label htmlFor="expiry_days">Expiry Alert Days</Label>
                                <Input
                                    id="expiry_days"
                                    type="number"
                                    min="1"
                                    value={expiryDays}
                                    onChange={(e) => setExpiryDays(parseInt(e.target.value) || 30)}
                                    className="w-32"
                                />
                            </div>
                            <Button onClick={handleFilterUpdate}>
                                Update Alerts
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Out of Stock Medicines */}
                {out_of_stock_medicines.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                {getAlertIcon('out_of_stock')}
                                <span>Out of Stock Medicines</span>
                            </CardTitle>
                            <CardDescription>
                                Medicines that are completely out of stock
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Medicine</TableHead>
                                        <TableHead>Code</TableHead>
                                        <TableHead>Category</TableHead>
                                        <TableHead>Supplier</TableHead>
                                        <TableHead>Stock</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {out_of_stock_medicines.map((medicine) => (
                                        <TableRow key={medicine.id}>
                                            <TableCell className="font-medium">{medicine.name}</TableCell>
                                            <TableCell>{medicine.code}</TableCell>
                                            <TableCell>{medicine.category.name}</TableCell>
                                            <TableCell>{medicine.supplier.name}</TableCell>
                                            <TableCell>
                                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    {medicine.stock_quantity}
                                                </span>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                )}

                {/* Low Stock Medicines */}
                {low_stock_medicines.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                {getAlertIcon('low_stock')}
                                <span>Low Stock Medicines</span>
                            </CardTitle>
                            <CardDescription>
                                Medicines with stock below threshold ({lowStockThreshold} units)
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Medicine</TableHead>
                                        <TableHead>Code</TableHead>
                                        <TableHead>Category</TableHead>
                                        <TableHead>Supplier</TableHead>
                                        <TableHead>Stock</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {low_stock_medicines.map((medicine) => (
                                        <TableRow key={medicine.id}>
                                            <TableCell className="font-medium">{medicine.name}</TableCell>
                                            <TableCell>{medicine.code}</TableCell>
                                            <TableCell>{medicine.category.name}</TableCell>
                                            <TableCell>{medicine.supplier.name}</TableCell>
                                            <TableCell>
                                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    {medicine.stock_quantity}
                                                </span>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                )}

                {/* Expired Batches */}
                {expired_batches.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                {getAlertIcon('expired')}
                                <span>Expired Batches</span>
                            </CardTitle>
                            <CardDescription>
                                Batches that have already expired
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Medicine</TableHead>
                                        <TableHead>Batch Number</TableHead>
                                        <TableHead>Expiry Date</TableHead>
                                        <TableHead>Quantity</TableHead>
                                        <TableHead>Days Expired</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {expired_batches.map((batch) => (
                                        <TableRow key={batch.id}>
                                            <TableCell className="font-medium">
                                                {batch.medicine.name}
                                                <p className="text-sm text-muted-foreground">{batch.medicine.code}</p>
                                            </TableCell>
                                            <TableCell>{batch.batch_number}</TableCell>
                                            <TableCell>{format(new Date(batch.expiry_date), 'MMM dd, yyyy')}</TableCell>
                                            <TableCell>{batch.quantity}</TableCell>
                                            <TableCell>
                                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                    {Math.abs(getDaysUntilExpiry(batch.expiry_date))} days
                                                </span>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                )}

                {/* Expiring Soon Batches */}
                {expiring_soon_batches.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                {getAlertIcon('expiring')}
                                <span>Expiring Soon Batches</span>
                            </CardTitle>
                            <CardDescription>
                                Batches expiring within {expiryDays} days
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Medicine</TableHead>
                                        <TableHead>Batch Number</TableHead>
                                        <TableHead>Expiry Date</TableHead>
                                        <TableHead>Quantity</TableHead>
                                        <TableHead>Days Until Expiry</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {expiring_soon_batches.map((batch) => (
                                        <TableRow key={batch.id}>
                                            <TableCell className="font-medium">
                                                {batch.medicine.name}
                                                <p className="text-sm text-muted-foreground">{batch.medicine.code}</p>
                                            </TableCell>
                                            <TableCell>{batch.batch_number}</TableCell>
                                            <TableCell>{format(new Date(batch.expiry_date), 'MMM dd, yyyy')}</TableCell>
                                            <TableCell>{batch.quantity}</TableCell>
                                            <TableCell>
                                                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                                    {getDaysUntilExpiry(batch.expiry_date)} days
                                                </span>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                )}

                {/* No Alerts Message */}
                {summary.total_alerts === 0 && (
                    <Card>
                        <CardContent className="p-8 text-center">
                            <div className="flex flex-col items-center space-y-4">
                                <div className="rounded-full bg-green-100 p-3">
                                    <Package className="h-8 w-8 text-green-600" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold">All Good!</h3>
                                    <p className="text-muted-foreground">
                                        No inventory alerts at this time. All medicines are well-stocked and within expiry limits.
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
