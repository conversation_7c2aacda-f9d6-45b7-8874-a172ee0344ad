<?php

namespace App\Services;

use App\Models\Batch;
use App\Models\Medicine;
use App\Models\Payment;
use App\Models\Sale;
use App\Models\SaleItem;
use Illuminate\Database\DatabaseManager;
use Illuminate\Support\Arr;
use RuntimeException;

class SaleService
{
    public function __construct(public DatabaseManager $db) {}

    /**
     * @param  array<int, array{medicine_id:int, quantity:int, unit_price:float|int}>  $items
     * @param  array{type: 'percent'|'fixed'|null, value: float|int}  $discount
     * @param  array<int, array{method:string, amount:float|int}>  $payments
     */
    public function createSale(int $cashierId, array $items, array $discount = ['type' => null, 'value' => 0], array $payments = [['method' => 'cash', 'amount' => 0]]): Sale
    {
        return $this->db->transaction(function () use ($cashierId, $items, $discount, $payments) {
            $subtotal = 0.0;

            $sale = Sale::query()->create([
                'cashier_id' => $cashierId,
                'subtotal' => 0,
                'discount_type' => $discount['type'],
                'discount_value' => (float) $discount['value'],
                'total' => 0,
            ]);

            foreach ($items as $item) {
                $medicine = Medicine::query()->findOrFail($item['medicine_id']);
                $qty = (int) $item['quantity'];
                $unit = (float) $item['unit_price'];

                $this->deductFromBatches($medicine->id, $qty);

                $lineTotal = $qty * $unit;
                $subtotal += $lineTotal;

                SaleItem::query()->create([
                    'sale_id' => $sale->id,
                    'medicine_id' => $medicine->id,
                    'quantity' => $qty,
                    'unit_price' => $unit,
                    'line_total' => $lineTotal,
                ]);

                // keep medicine stock_quantity in sync
                $medicine->decrement('stock_quantity', $qty);
            }

            $sale->subtotal = $subtotal;
            $sale->total = $this->applyDiscount($subtotal, $discount['type'], (float) $discount['value']);
            $sale->save();

            foreach ($payments as $p) {
                Payment::query()->create([
                    'sale_id' => $sale->id,
                    'method' => (string) Arr::get($p, 'method', 'cash'),
                    'amount' => (float) Arr::get($p, 'amount', 0),
                ]);
            }

            return $sale->load(['items', 'payments']);
        });
    }

    protected function applyDiscount(float $subtotal, ?string $type, float $value): float
    {
        if ($type === 'percent') {
            return max(0.0, round($subtotal * (1 - ($value / 100)), 2));
        }

        if ($type === 'fixed') {
            return max(0.0, round($subtotal - $value, 2));
        }

        return round($subtotal, 2);
    }

    protected function deductFromBatches(int $medicineId, int $requiredQty): void
    {
        $remaining = $requiredQty;

        // FEFO style: earliest expiry first to satisfy pharmacy requirement
        $batches = Batch::query()
            ->where('medicine_id', $medicineId)
            ->orderBy('expiry_date')
            ->lockForUpdate()
            ->get();

        $totalAvailable = (int) $batches->sum('quantity');
        if ($totalAvailable < $requiredQty) {
            throw new RuntimeException("Insufficient stock for medicine {$medicineId}. Needed {$requiredQty}, available {$totalAvailable}.");
        }

        foreach ($batches as $batch) {
            if ($remaining <= 0) {
                break;
            }

            $take = min($remaining, (int) $batch->quantity);
            if ($take > 0) {
                $batch->decrement('quantity', $take);
                $remaining -= $take;
            }
        }

        if ($remaining > 0) {
            throw new RuntimeException('Failed to deduct enough stock from batches.');
        }
    }
}
