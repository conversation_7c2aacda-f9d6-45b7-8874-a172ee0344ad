import React, { lazy, Suspense, useState, useCallback } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';

// Lazy load Chart component with retry capability
const Chart = lazy(() => import('react-apexcharts'));

interface ChartWrapperProps {
    options: any;
    series: any;
    type: string;
    height?: number;
    width?: number | string;
}

interface ChartErrorBoundaryState {
    hasError: boolean;
    error?: Error;
}

class ChartErrorBoundary extends React.Component<
    React.PropsWithChildren<{ onRetry: () => void }>,
    ChartErrorBoundaryState
> {
    constructor(props: React.PropsWithChildren<{ onRetry: () => void }>) {
        super(props);
        this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error): ChartErrorBoundaryState {
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        console.error('Chart Error:', error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <div className="flex h-[350px] items-center justify-center p-4">
                    <Alert className="max-w-md">
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription className="mt-2">
                            <div className="space-y-2">
                                <p>Failed to load chart. Please try again.</p>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                        this.setState({ hasError: false });
                                        this.props.onRetry();
                                    }}
                                    className="w-full"
                                >
                                    <RefreshCw className="mr-2 h-4 w-4" />
                                    Retry
                                </Button>
                            </div>
                        </AlertDescription>
                    </Alert>
                </div>
            );
        }

        return this.props.children;
    }
}

export default function ChartWrapper({ options, series, type, height = 350, width }: ChartWrapperProps) {
    const [retryKey, setRetryKey] = useState(0);

    const handleRetry = useCallback(() => {
        setRetryKey(prev => prev + 1);
    }, []);

    const ChartFallback = () => (
        <div className="flex h-[350px] items-center justify-center">
            <div className="flex flex-col items-center space-y-2">
                <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
                <p className="text-sm text-muted-foreground">Loading chart...</p>
            </div>
        </div>
    );

    return (
        <ChartErrorBoundary onRetry={handleRetry}>
            <Suspense fallback={<ChartFallback />}>
                <Chart
                    key={retryKey}
                    options={options}
                    series={series}
                    type={type}
                    height={height}
                    width={width}
                />
            </Suspense>
        </ChartErrorBoundary>
    );
}
