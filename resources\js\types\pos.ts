export interface Medicine {
    id: number;
    name: string;
    code: string;
    selling_price: number;
    stock_quantity: number;
    category_id: number;
    category: {
        id: number;
        name: string;
    };
}

export interface CartItem {
    id: number;
    medicine: Medicine;
    quantity: number;
    unit_price: number;
    line_total: number;
}

export interface Customer {
    id: number;
    name: string;
    email?: string;
    phone?: string;
    address?: string;
}

export interface DiscountInfo {
    type: 'none' | 'percentage' | 'fixed';
    value: number;
    amount: number;
}

export interface PaymentMethod {
    method: 'cash' | 'mobile' | 'insurance';
    amount: number;
}

export interface PosState {
    cart: CartItem[];
    customer?: Customer;
    discount: DiscountInfo;
    payments: PaymentMethod[];
    subtotal: number;
    total: number;
    change: number;
}

export interface PosIndexProps {
    medicines: Medicine[];
}
