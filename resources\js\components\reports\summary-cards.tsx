import { Card, CardContent } from '@/components/ui/card';
import type { Summary } from '@/types/reports';
import { DollarSign, Package, ShoppingCart, TrendingUp } from 'lucide-react';

interface SummaryCardsProps {
    summary: Summary;
    formatCurrency: (amount: number) => string;
}

export default function SummaryCards({ summary, formatCurrency }: SummaryCardsProps) {
    const cards = [
        {
            title: 'Total Sales',
            value: summary.total_sales.toString(),
            icon: <ShoppingCart className="h-5 w-5 text-blue-500" />,
            colorClass: '',
        },
        {
            title: 'Total Revenue',
            value: formatCurrency(summary.total_revenue),
            icon: <DollarSign className="h-5 w-5 text-green-500" />,
            colorClass: 'text-green-600',
        },
        {
            title: 'Avg Sale Value',
            value: formatCurrency(summary.average_sale_value),
            icon: <TrendingUp className="h-5 w-5 text-purple-500" />,
            colorClass: 'text-purple-600',
        },
        {
            title: 'Items Sold',
            value: summary.total_items_sold.toString(),
            icon: <Package className="h-5 w-5 text-orange-500" />,
            colorClass: 'text-orange-600',
        },
    ];

    return (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            {cards.map((card) => (
                <Card key={card.title}>
                    <CardContent className="p-4">
                        <div className="flex items-center space-x-2">
                            {card.icon}
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">{card.title}</p>
                                <p className={`text-2xl font-bold ${card.colorClass}`}>{card.value}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            ))}
        </div>
    );
}
