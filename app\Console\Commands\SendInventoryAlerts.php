<?php

namespace App\Console\Commands;

use App\Models\Batch;
use App\Models\Medicine;
use Illuminate\Console\Command;

class SendInventoryAlerts extends Command
{
    protected $signature = 'inventory:alerts';

    protected $description = 'Send low-stock and near-expiry alerts (logs only for now)';

    public function handle(): int
    {
        $lowStock = Medicine::query()->where('stock_quantity', '<', 10)->get(['id', 'name', 'stock_quantity']);
        $nearExpiry = Batch::query()->whereBetween('expiry_date', [now()->toDateString(), now()->addMonths(1)->toDateString()])->get(['id', 'medicine_id', 'expiry_date', 'quantity']);

        foreach ($lowStock as $m) {
            $this->info("LOW STOCK: {$m->name} ({$m->stock_quantity})");
        }

        foreach ($nearExpiry as $b) {
            $this->info("NEAR EXPIRY: Medicine {$b->medicine_id} batch {$b->id} expires {$b->expiry_date} (qty {$b->quantity})");
        }

        return self::SUCCESS;
    }
}
