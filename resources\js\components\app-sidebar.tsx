import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { dashboard } from '@/routes';
import { index as categoriesIndex } from '@/routes/categories';
import { index as medicinesIndex } from '@/routes/medicines';
import { index as purchasesIndex } from '@/routes/purchases';
import { index as suppliersIndex } from '@/routes/suppliers';
import { type NavItem, type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { AlertTriangle, LayoutGrid, Package, Pill, ShoppingCart, TrendingUp, Users, Warehouse } from 'lucide-react';
import AppLogo from './app-logo';

// This will be replaced with dynamic navigation items

export function AppSidebar() {
    const { auth } = usePage<SharedData>().props;
    const isAdmin = auth.user?.role === 'admin';

    // All navigation items organized properly
    const navigationItems: NavItem[] = [
        {
            title: 'Dashboard',
            href: dashboard(),
            icon: LayoutGrid,
        },
        {
            title: 'POS (Sales)',
            href: '/pos',
            icon: ShoppingCart,
        },
        {
            title: 'Alerts',
            href: '/alerts',
            icon: AlertTriangle,
        },
        {
            title: 'Reports',
            href: '/reports',
            icon: TrendingUp,
        },
        {
            title: 'Customers',
            href: '/customers',
            icon: Users,
        },
    ];

    // Inventory management section
    const inventoryItems: NavItem[] = [
        {
            title: 'Categories',
            href: categoriesIndex(),
            icon: Package,
        },
        {
            title: 'Medicines',
            href: medicinesIndex(),
            icon: Pill,
        },
        {
            title: 'Suppliers',
            href: suppliersIndex(),
            icon: Users,
        },
    ];

    // Admin-only items
    const adminItems: NavItem[] = [
        {
            title: 'Purchases',
            href: purchasesIndex().url,
            icon: Warehouse,
        },
    ];

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href={dashboard()} prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain items={navigationItems} />
                <NavMain items={inventoryItems} />
                {isAdmin && <NavMain items={adminItems} />}
            </SidebarContent>

            <SidebarFooter>
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
