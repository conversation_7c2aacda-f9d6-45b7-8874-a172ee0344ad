'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import type { Customer, DiscountInfo, PaymentMethod } from '@/types/pos';
import { Calculator, CreditCard, DollarSign, Receipt, ShieldCheck, Smartphone } from 'lucide-react';
import { useState } from 'react';

interface CheckoutPanelProps {
    subtotal: number;
    discount: DiscountInfo;
    total: number;
    payments: PaymentMethod[];
    customer?: Customer;
    onUpdateDiscount: (discount: DiscountInfo) => void;
    onUpdatePayments: (payments: PaymentMethod[]) => void;
    onSelectCustomer: (customer?: Customer) => void;
    onCompleteTransaction: () => void;
    isProcessing?: boolean;
    className?: string;
}

export function CheckoutPanel({
    subtotal,
    discount,
    total,
    payments,
    customer,
    onUpdateDiscount,
    onUpdatePayments,
    onSelectCustomer,
    onCompleteTransaction,
    isProcessing = false,
    className,
}: CheckoutPanelProps) {
    const [discountType, setDiscountType] = useState<'none' | 'percentage' | 'fixed'>(discount.type);
    const [discountValue, setDiscountValue] = useState(discount.value.toString());
    const [paymentMethod, setPaymentMethod] = useState<'cash' | 'mobile' | 'insurance'>('cash');
    const [paymentAmount, setPaymentAmount] = useState('');

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(price);
    };

    const calculateDiscount = (type: string, value: number, subtotal: number) => {
        if (type === 'percentage') {
            return (subtotal * Math.min(value, 100)) / 100;
        } else if (type === 'fixed') {
            return Math.min(value, subtotal);
        }
        return 0;
    };

    const handleDiscountChange = () => {
        const value = parseFloat(discountValue) || 0;
        const amount = calculateDiscount(discountType, value, subtotal);
        onUpdateDiscount({
            type: discountType,
            value,
            amount,
        });
    };

    const handleAddPayment = () => {
        const amount = parseFloat(paymentAmount) || 0;
        if (amount > 0) {
            const newPayments = [...payments, { method: paymentMethod, amount }];
            onUpdatePayments(newPayments);
            setPaymentAmount('');
        }
    };

    const handleRemovePayment = (index: number) => {
        const newPayments = payments.filter((_, i) => i !== index);
        onUpdatePayments(newPayments);
    };

    const totalPaid = payments.reduce((sum, payment) => sum + payment.amount, 0);
    const change = Math.max(0, totalPaid - total);
    const remainingAmount = Math.max(0, total - totalPaid);
    const canCompleteTransaction = remainingAmount <= 0 && total > 0;

    const paymentIcons = {
        cash: DollarSign,
        mobile: Smartphone,
        insurance: ShieldCheck,
    };

    return (
        <Card className={cn('flex h-full flex-col', className)}>
            <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2">
                    <Calculator className="h-5 w-5" />
                    Checkout
                </CardTitle>
            </CardHeader>

            <CardContent className="flex flex-1 flex-col space-y-6">
                {/* Customer Selection */}
                <div className="space-y-2">
                    <Label>Customer (Optional)</Label>
                    <div className="flex gap-2">
                        <Select onValueChange={(value) => onSelectCustomer(value === 'none' ? undefined : { id: 1, name: value })}>
                            <SelectTrigger>
                                <SelectValue placeholder={customer?.name || 'Walk-in customer'} />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="none">Walk-in customer</SelectItem>
                                <SelectItem value="John Doe">John Doe</SelectItem>
                                <SelectItem value="Jane Smith">Jane Smith</SelectItem>
                            </SelectContent>
                        </Select>
                        <Button variant="outline" size="sm">
                            New
                        </Button>
                    </div>
                </div>

                {/* Discount Section */}
                <div className="space-y-3">
                    <Label>Discount</Label>
                    <div className="space-y-2">
                        <RadioGroup
                            value={discountType}
                            onValueChange={(value: 'none' | 'percentage' | 'fixed') => {
                                setDiscountType(value);
                                if (value === 'none') {
                                    setDiscountValue('0');
                                    onUpdateDiscount({ type: 'none', value: 0, amount: 0 });
                                }
                            }}
                            className="flex gap-4"
                        >
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="none" id="no-discount" />
                                <Label htmlFor="no-discount">None</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="percentage" id="percentage" />
                                <Label htmlFor="percentage">%</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="fixed" id="fixed" />
                                <Label htmlFor="fixed">Fixed</Label>
                            </div>
                        </RadioGroup>

                        {discountType !== 'none' && (
                            <div className="flex gap-2">
                                <Input
                                    type="number"
                                    placeholder={discountType === 'percentage' ? 'Enter %' : 'Enter amount'}
                                    value={discountValue}
                                    onChange={(e) => setDiscountValue(e.target.value)}
                                    onBlur={handleDiscountChange}
                                    className="flex-1"
                                />
                                <Button variant="outline" size="sm" onClick={handleDiscountChange}>
                                    Apply
                                </Button>
                            </div>
                        )}
                    </div>
                </div>

                {/* Order Summary */}
                <div className="space-y-2 rounded-lg bg-muted/50 p-4">
                    <div className="flex justify-between text-sm">
                        <span>Subtotal:</span>
                        <span>{formatPrice(subtotal)}</span>
                    </div>
                    {discount.amount > 0 && (
                        <div className="flex justify-between text-sm text-green-600">
                            <span>Discount:</span>
                            <span>-{formatPrice(discount.amount)}</span>
                        </div>
                    )}
                    <div className="flex justify-between border-t pt-2 text-lg font-semibold">
                        <span>Total:</span>
                        <span>{formatPrice(total)}</span>
                    </div>
                </div>

                {/* Payment Section */}
                <div className="space-y-3">
                    <Label>Payment</Label>
                    <div className="flex gap-2">
                        <Select value={paymentMethod} onValueChange={(value: 'cash' | 'mobile' | 'insurance') => setPaymentMethod(value)}>
                            <SelectTrigger className="w-32">
                                <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="cash">Cash</SelectItem>
                                <SelectItem value="mobile">Mobile</SelectItem>
                                <SelectItem value="insurance">Insurance</SelectItem>
                            </SelectContent>
                        </Select>
                        <Input
                            type="number"
                            placeholder="Amount"
                            value={paymentAmount}
                            onChange={(e) => setPaymentAmount(e.target.value)}
                            className="flex-1"
                        />
                        <Button variant="outline" size="sm" onClick={handleAddPayment} disabled={!paymentAmount}>
                            Add
                        </Button>
                    </div>

                    {payments.length > 0 && (
                        <div className="space-y-2">
                            {payments.map((payment, index) => {
                                const Icon = paymentIcons[payment.method];
                                return (
                                    <div key={index} className="flex items-center justify-between rounded border bg-card p-2">
                                        <div className="flex items-center gap-2">
                                            <Icon className="h-4 w-4" />
                                            <span className="text-sm capitalize">{payment.method}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <span className="text-sm font-medium">{formatPrice(payment.amount)}</span>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => handleRemovePayment(index)}
                                                className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                                            >
                                                ×
                                            </Button>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    )}
                </div>

                {/* Payment Summary */}
                {(totalPaid > 0 || remainingAmount > 0) && (
                    <div className="space-y-2 rounded-lg bg-muted/30 p-3 text-sm">
                        <div className="flex justify-between">
                            <span>Total Paid:</span>
                            <span className="font-medium">{formatPrice(totalPaid)}</span>
                        </div>
                        {remainingAmount > 0 && (
                            <div className="flex justify-between text-orange-600">
                                <span>Remaining:</span>
                                <span className="font-medium">{formatPrice(remainingAmount)}</span>
                            </div>
                        )}
                        {change > 0 && (
                            <div className="flex justify-between text-green-600">
                                <span>Change:</span>
                                <span className="font-medium">{formatPrice(change)}</span>
                            </div>
                        )}
                    </div>
                )}

                <div className="mt-auto pt-4">
                    <Button className="w-full" size="lg" onClick={onCompleteTransaction} disabled={!canCompleteTransaction || isProcessing}>
                        <Receipt className="mr-2 h-4 w-4" />
                        {isProcessing ? 'Processing...' : 'Complete Sale'}
                    </Button>
                </div>
            </CardContent>
        </Card>
    );
}
