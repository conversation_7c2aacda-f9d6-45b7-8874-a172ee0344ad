'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import AppLayout from '@/layouts/app-layout';
import { MedicineSearch } from '@/components/pos/medicine-search';
import { ShoppingCart } from '@/components/pos/shopping-cart';
import { CheckoutPanel } from '@/components/pos/checkout-panel';
import { type BreadcrumbItem } from '@/types';
import type { Medicine, CartItem, DiscountInfo, PaymentMethod, Customer, PosState } from '@/types/pos';
import { Head, router } from '@inertiajs/react';
import { ShoppingCart as ShoppingCartIcon, Users, Package, TrendingUp } from 'lucide-react';

interface PosIndexProps {
    medicines: Medicine[];
}

export default function PosIndex({ medicines }: PosIndexProps) {
    const breadcrumbs: BreadcrumbItem[] = [{ title: 'Point of Sale', href: '#' }];

    // POS State Management
    const [posState, setPosState] = useState<PosState>({
        cart: [],
        discount: { type: 'none', value: 0, amount: 0 },
        payments: [],
        subtotal: 0,
        total: 0,
        change: 0
    });

    const [isProcessing, setIsProcessing] = useState(false);

    // Calculate totals whenever cart or discount changes
    useEffect(() => {
        const subtotal = posState.cart.reduce((sum, item) => sum + item.line_total, 0);
        const total = Math.max(0, subtotal - posState.discount.amount);
        const totalPaid = posState.payments.reduce((sum, payment) => sum + payment.amount, 0);
        const change = Math.max(0, totalPaid - total);

        setPosState(prev => ({
            ...prev,
            subtotal,
            total,
            change
        }));
    }, [posState.cart, posState.discount, posState.payments]);

    const addToCart = (medicine: Medicine) => {
        const existingItem = posState.cart.find(item => item.medicine.id === medicine.id);
        
        if (existingItem) {
            // Increase quantity if already in cart
            updateCartItemQuantity(existingItem.id, existingItem.quantity + 1);
        } else {
            // Add new item to cart
            const newItem: CartItem = {
                id: Date.now(), // Simple ID generation
                medicine,
                quantity: 1,
                unit_price: medicine.selling_price,
                line_total: medicine.selling_price
            };
            
            setPosState(prev => ({
                ...prev,
                cart: [...prev.cart, newItem]
            }));
        }
    };

    const updateCartItemQuantity = (id: number, quantity: number) => {
        setPosState(prev => ({
            ...prev,
            cart: prev.cart.map(item => 
                item.id === id 
                    ? { ...item, quantity, line_total: item.unit_price * quantity }
                    : item
            )
        }));
    };

    const removeCartItem = (id: number) => {
        setPosState(prev => ({
            ...prev,
            cart: prev.cart.filter(item => item.id !== id)
        }));
    };

    const clearCart = () => {
        setPosState(prev => ({
            ...prev,
            cart: [],
            discount: { type: 'none', value: 0, amount: 0 },
            payments: []
        }));
    };

    const updateDiscount = (discount: DiscountInfo) => {
        setPosState(prev => ({ ...prev, discount }));
    };

    const updatePayments = (payments: PaymentMethod[]) => {
        setPosState(prev => ({ ...prev, payments }));
    };

    const selectCustomer = (customer?: Customer) => {
        setPosState(prev => ({ ...prev, customer }));
    };

    const completeTransaction = async () => {
        if (posState.cart.length === 0 || posState.total === 0) return;

        setIsProcessing(true);
        
        try {
            const saleData = {
                items: posState.cart.map(item => ({
                    medicine_id: item.medicine.id,
                    quantity: item.quantity,
                    unit_price: item.unit_price,
                    line_total: item.line_total
                })),
                discount_type: posState.discount.type,
                discount_value: posState.discount.value,
                payments: posState.payments.length > 0 ? posState.payments : [
                    { method: 'cash', amount: posState.total }
                ],
                customer_id: posState.customer?.id || null,
                total: posState.total
            };

            router.post('/pos', saleData, {
                onSuccess: () => {
                    clearCart();
                },
                onError: (errors) => {
                    console.error('Sale failed:', errors);
                },
                onFinish: () => {
                    setIsProcessing(false);
                }
            });
        } catch (error) {
            console.error('Transaction failed:', error);
            setIsProcessing(false);
        }
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(price);
    };

    const availableMedicines = medicines.filter(medicine => medicine.stock_quantity > 0);
    const lowStockCount = medicines.filter(medicine => medicine.stock_quantity <= 10).length;

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Point of Sale" />
            
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold">Point of Sale</h1>
                        <p className="text-muted-foreground">Process sales and manage transactions</p>
                    </div>
                    
                    <div className="flex items-center gap-4">
                        <Badge variant="outline" className="flex items-center gap-1">
                            <Package className="h-3 w-3" />
                            {availableMedicines.length} Available
                        </Badge>
                        {lowStockCount > 0 && (
                            <Badge variant="destructive" className="flex items-center gap-1">
                                <TrendingUp className="h-3 w-3" />
                                {lowStockCount} Low Stock
                            </Badge>
                        )}
                    </div>
                </div>

                {/* Quick Stats */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Cart Items</CardTitle>
                            <ShoppingCartIcon className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{posState.cart.length}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Subtotal</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatPrice(posState.subtotal)}</div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Discount</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">
                                {formatPrice(posState.discount.amount)}
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-primary">
                                {formatPrice(posState.total)}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Main POS Interface */}
                <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 min-h-[600px]">
                    {/* Left Column - Medicine Search & Cart */}
                    <div className="lg:col-span-8 space-y-6">
                        {/* Medicine Search */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Add Medicines</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <MedicineSearch 
                                    medicines={availableMedicines}
                                    onAddToCart={addToCart}
                                />
                            </CardContent>
                        </Card>

                        {/* Shopping Cart */}
                        <ShoppingCart
                            items={posState.cart}
                            subtotal={posState.subtotal}
                            onUpdateQuantity={updateCartItemQuantity}
                            onRemoveItem={removeCartItem}
                            onClearCart={clearCart}
                        />
                    </div>

                    {/* Right Column - Checkout */}
                    <div className="lg:col-span-4">
                        <CheckoutPanel
                            subtotal={posState.subtotal}
                            discount={posState.discount}
                            total={posState.total}
                            payments={posState.payments}
                            customer={posState.customer}
                            onUpdateDiscount={updateDiscount}
                            onUpdatePayments={updatePayments}
                            onSelectCustomer={selectCustomer}
                            onCompleteTransaction={completeTransaction}
                            isProcessing={isProcessing}
                        />
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
