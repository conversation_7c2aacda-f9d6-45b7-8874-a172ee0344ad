import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { destroy as purchasesDestroy, index as purchasesIndex } from '@/routes/purchases';
import { type BreadcrumbItem, type SharedData } from '@/types';
import { Head, router, usePage } from '@inertiajs/react';
import { format } from 'date-fns';
import { ArrowLeft, Trash2 } from 'lucide-react';

interface Supplier {
    id: number;
    name: string;
    email?: string;
    phone?: string;
}

interface Medicine {
    id: number;
    name: string;
    code: string;
}

interface PurchaseItem {
    id: number;
    quantity: number;
    unit_cost: number;
    line_total: number;
    medicine: Medicine;
}

interface Purchase {
    id: number;
    invoice_number: string;
    purchased_at: string;
    total_cost: number;
    supplier: Supplier;
    items: PurchaseItem[];
    created_at: string;
}

interface PurchasesShowProps {
    purchase: Purchase;
}

export default function PurchasesShow({ purchase }: PurchasesShowProps) {
    const { auth } = usePage<SharedData>().props;
    const isAdmin = auth.user?.role === 'admin';

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Purchases', href: purchasesIndex().url },
        { title: purchase.invoice_number, href: '#' },
    ];

    const handleDelete = () => {
        if (confirm(`Are you sure you want to delete purchase ${purchase.invoice_number}?`)) {
            router.delete(purchasesDestroy(purchase.id).url);
        }
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Purchase ${purchase.invoice_number}`} />
            
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Button variant="outline" onClick={() => router.visit(purchasesIndex().url)}>
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            Back to Purchases
                        </Button>
                        <div>
                            <h1 className="text-2xl font-bold">Purchase {purchase.invoice_number}</h1>
                            <p className="text-muted-foreground">
                                Created on {format(new Date(purchase.created_at), 'MMM dd, yyyy')}
                            </p>
                        </div>
                    </div>
                    
                    {isAdmin && (
                        <Button variant="destructive" onClick={handleDelete}>
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Purchase
                        </Button>
                    )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Purchase Details</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Invoice Number</p>
                                <p className="text-lg font-semibold">{purchase.invoice_number}</p>
                            </div>
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Purchase Date</p>
                                <p>{format(new Date(purchase.purchased_at), 'MMMM dd, yyyy')}</p>
                            </div>
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Total Cost</p>
                                <p className="text-2xl font-bold text-green-600">
                                    {formatCurrency(purchase.total_cost)}
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Supplier Information</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">Supplier Name</p>
                                <p className="text-lg font-semibold">{purchase.supplier.name}</p>
                            </div>
                            {purchase.supplier.email && (
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Email</p>
                                    <p>{purchase.supplier.email}</p>
                                </div>
                            )}
                            {purchase.supplier.phone && (
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Phone</p>
                                    <p>{purchase.supplier.phone}</p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Purchase Items</CardTitle>
                        <CardDescription>
                            Items included in this purchase order
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Medicine</TableHead>
                                    <TableHead>Code</TableHead>
                                    <TableHead>Quantity</TableHead>
                                    <TableHead>Unit Cost</TableHead>
                                    <TableHead>Line Total</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {purchase.items.map((item) => (
                                    <TableRow key={item.id}>
                                        <TableCell className="font-medium">
                                            {item.medicine.name}
                                        </TableCell>
                                        <TableCell>{item.medicine.code}</TableCell>
                                        <TableCell>{item.quantity}</TableCell>
                                        <TableCell>{formatCurrency(item.unit_cost)}</TableCell>
                                        <TableCell className="font-medium">
                                            {formatCurrency(item.line_total)}
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>

                        <div className="mt-6 flex justify-end">
                            <div className="text-right space-y-2">
                                <div className="flex justify-between items-center min-w-[200px]">
                                    <span className="font-medium">Items:</span>
                                    <span>{purchase.items.length}</span>
                                </div>
                                <div className="flex justify-between items-center min-w-[200px]">
                                    <span className="font-medium">Total Quantity:</span>
                                    <span>{purchase.items.reduce((sum, item) => sum + item.quantity, 0)}</span>
                                </div>
                                <div className="flex justify-between items-center min-w-[200px] text-lg font-bold border-t pt-2">
                                    <span>Total Cost:</span>
                                    <span className="text-green-600">{formatCurrency(purchase.total_cost)}</span>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
