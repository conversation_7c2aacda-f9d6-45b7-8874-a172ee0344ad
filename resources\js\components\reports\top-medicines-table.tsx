import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import type { TopMedicine } from '@/types/reports';

interface TopMedicinesTableProps {
    topMedicines: TopMedicine[];
    formatCurrency: (amount: number) => string;
}

export default function TopMedicinesTable({ topMedicines, formatCurrency }: TopMedicinesTableProps) {
    return (
        <Card>
            <CardHeader>
                <CardTitle>Top Selling Medicines</CardTitle>
                <CardDescription>Best performing products by quantity sold</CardDescription>
            </CardHeader>
            <CardContent>
                {topMedicines.length > 0 ? (
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Medicine</TableHead>
                                <TableHead>Quantity</TableHead>
                                <TableHead>Revenue</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {topMedicines.map((item) => (
                                <TableRow key={item.medicine.id}>
                                    <TableCell>
                                        <div>
                                            <p className="font-medium">{item.medicine.name}</p>
                                            <p className="text-sm text-muted-foreground">{item.medicine.code}</p>
                                        </div>
                                    </TableCell>
                                    <TableCell>
                                        <span className="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
                                            {item.total_quantity}
                                        </span>
                                    </TableCell>
                                    <TableCell className="font-medium">{formatCurrency(item.total_revenue)}</TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                ) : (
                    <div className="py-8 text-center text-muted-foreground">No sales data available for the selected period</div>
                )}
            </CardContent>
        </Card>
    );
}
