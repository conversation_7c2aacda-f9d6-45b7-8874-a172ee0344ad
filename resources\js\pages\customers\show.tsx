import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type PaginatedData } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { format } from 'date-fns';
import { ArrowLeft, Calendar, Mail, MapPin, Phone, ShoppingBag, User } from 'lucide-react';

interface Medicine {
    id: number;
    name: string;
    code: string;
}

interface SaleItem {
    id: number;
    quantity: number;
    unit_price: number;
    line_total: number;
    medicine: Medicine;
}

interface Payment {
    id: number;
    method: string;
    amount: number;
}

interface Cashier {
    id: number;
    name: string;
}

interface Sale {
    id: number;
    sold_at: string;
    subtotal: number;
    discount_type: string | null;
    discount_value: number;
    total: number;
    items: SaleItem[];
    payments: Payment[];
    cashier: Cashier;
}

interface Customer {
    id: number;
    name: string;
    email?: string;
    phone?: string;
    address?: string;
    date_of_birth?: string;
    gender?: 'male' | 'female' | 'other';
    notes?: string;
    total_spent: number;
    total_purchases: number;
    created_at: string;
}

interface CustomerShowProps {
    customer: Customer;
    sales_history: PaginatedData<Sale>;
}

export default function CustomerShow({ customer, sales_history }: CustomerShowProps) {
    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Customers', href: route('customers.index') },
        { title: customer.name, href: '#' },
    ];

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title={`Customer: ${customer.name}`} />
            
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                        <Link href={route('customers.index')}>
                            <Button variant="outline">
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Back to Customers
                            </Button>
                        </Link>
                        <div>
                            <h1 className="text-2xl font-bold">{customer.name}</h1>
                            <p className="text-muted-foreground">
                                Customer since {format(new Date(customer.created_at), 'MMMM yyyy')}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Customer Information */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <User className="h-5 w-5" />
                                    <span>Customer Information</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <p className="text-sm font-medium text-muted-foreground">Name</p>
                                        <p className="text-lg font-semibold">{customer.name}</p>
                                    </div>
                                    {customer.gender && (
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Gender</p>
                                            <p className="capitalize">{customer.gender}</p>
                                        </div>
                                    )}
                                </div>

                                {customer.email && (
                                    <div className="flex items-center space-x-2">
                                        <Mail className="h-4 w-4 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Email</p>
                                            <p>{customer.email}</p>
                                        </div>
                                    </div>
                                )}

                                {customer.phone && (
                                    <div className="flex items-center space-x-2">
                                        <Phone className="h-4 w-4 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Phone</p>
                                            <p>{customer.phone}</p>
                                        </div>
                                    </div>
                                )}

                                {customer.address && (
                                    <div className="flex items-start space-x-2">
                                        <MapPin className="h-4 w-4 text-muted-foreground mt-1" />
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Address</p>
                                            <p className="whitespace-pre-line">{customer.address}</p>
                                        </div>
                                    </div>
                                )}

                                {customer.date_of_birth && (
                                    <div className="flex items-center space-x-2">
                                        <Calendar className="h-4 w-4 text-muted-foreground" />
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Date of Birth</p>
                                            <p>{format(new Date(customer.date_of_birth), 'MMMM dd, yyyy')}</p>
                                        </div>
                                    </div>
                                )}

                                {customer.notes && (
                                    <div>
                                        <p className="text-sm font-medium text-muted-foreground">Notes</p>
                                        <p className="whitespace-pre-line">{customer.notes}</p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Purchase Summary */}
                    <div>
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center space-x-2">
                                    <ShoppingBag className="h-5 w-5" />
                                    <span>Purchase Summary</span>
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Purchases</p>
                                    <p className="text-2xl font-bold text-blue-600">{customer.total_purchases}</p>
                                </div>
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Spent</p>
                                    <p className="text-2xl font-bold text-green-600">
                                        {formatCurrency(customer.total_spent)}
                                    </p>
                                </div>
                                {customer.total_purchases > 0 && (
                                    <div>
                                        <p className="text-sm font-medium text-muted-foreground">Average Purchase</p>
                                        <p className="text-lg font-semibold">
                                            {formatCurrency(customer.total_spent / customer.total_purchases)}
                                        </p>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </div>

                {/* Purchase History */}
                <Card>
                    <CardHeader>
                        <CardTitle>Purchase History</CardTitle>
                        <CardDescription>
                            Complete transaction history for this customer
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {sales_history.data.length > 0 ? (
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Date</TableHead>
                                        <TableHead>Items</TableHead>
                                        <TableHead>Payment Method</TableHead>
                                        <TableHead>Cashier</TableHead>
                                        <TableHead>Total</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {sales_history.data.map((sale) => (
                                        <TableRow key={sale.id}>
                                            <TableCell>
                                                {format(new Date(sale.sold_at), 'MMM dd, yyyy HH:mm')}
                                            </TableCell>
                                            <TableCell>
                                                <div className="space-y-1">
                                                    {sale.items.slice(0, 2).map((item) => (
                                                        <div key={item.id} className="text-sm">
                                                            {item.medicine.name} × {item.quantity}
                                                        </div>
                                                    ))}
                                                    {sale.items.length > 2 && (
                                                        <div className="text-sm text-muted-foreground">
                                                            +{sale.items.length - 2} more items
                                                        </div>
                                                    )}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="space-y-1">
                                                    {sale.payments.map((payment) => (
                                                        <div key={payment.id} className="text-sm capitalize">
                                                            {payment.method}: {formatCurrency(payment.amount)}
                                                        </div>
                                                    ))}
                                                </div>
                                            </TableCell>
                                            <TableCell>{sale.cashier.name}</TableCell>
                                            <TableCell className="font-medium">
                                                {formatCurrency(sale.total)}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        ) : (
                            <div className="text-center py-8 text-muted-foreground">
                                No purchase history available
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
