<?php

namespace Tests\Unit;

use App\Models\Batch;
use App\Models\Category;
use App\Models\Medicine;
use App\Models\Supplier;
use App\Models\User;
use App\Services\SaleService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SaleServiceTest extends TestCase
{
    use RefreshDatabase;

    public function test_creates_sale_and_deducts_batches_fifo_and_applies_percent_discount(): void
    {
        $user = User::factory()->create();
        $category = Category::factory()->create();
        $supplier = Supplier::factory()->create();
        $med = Medicine::factory()->create([
            'category_id' => $category->id,
            'supplier_id' => $supplier->id,
            'stock_quantity' => 30,
            'purchase_price' => 2,
            'selling_price' => 5,
        ]);

        // two batches: earliest expiry gets used first
        $b1 = Batch::factory()->create(['medicine_id' => $med->id, 'quantity' => 10, 'expiry_date' => now()->addMonths(1)->toDateString()]);
        $b2 = Batch::factory()->create(['medicine_id' => $med->id, 'quantity' => 20, 'expiry_date' => now()->addMonths(6)->toDateString()]);

        $service = new SaleService(app('db'));

        $sale = $service->createSale($user->id, [
            ['medicine_id' => $med->id, 'quantity' => 12, 'unit_price' => 5],
        ], ['type' => 'percent', 'value' => 10], [
            ['method' => 'cash', 'amount' => 54],
        ]);

        // subtotal = 12*5 = 60; 10% off => total = 54
        $this->assertEquals(60.0, (float) $sale->subtotal);
        $this->assertEquals(54.0, (float) $sale->total);

        // batches: b1 -> 0 left, b2 -> 18 left
        $this->assertDatabaseHas('batches', ['id' => $b1->id, 'quantity' => 0]);
        $this->assertDatabaseHas('batches', ['id' => $b2->id, 'quantity' => 18]);

        // medicine's stock_quantity decremented aggregate
        $this->assertDatabaseHas('medicines', ['id' => $med->id, 'stock_quantity' => 18]);

        // sale persisted with item and payment
        $this->assertCount(1, $sale->items);
        $this->assertCount(1, $sale->payments);
    }

    public function test_fixed_discount_and_multi_payment(): void
    {
        $user = User::factory()->create();
        $category = Category::factory()->create();
        $supplier = Supplier::factory()->create();
        $med = Medicine::factory()->create([
            'category_id' => $category->id,
            'supplier_id' => $supplier->id,
            'stock_quantity' => 5,
            'purchase_price' => 2,
            'selling_price' => 10,
        ]);

        Batch::factory()->create(['medicine_id' => $med->id, 'quantity' => 5, 'expiry_date' => now()->addMonths(3)->toDateString()]);

        $service = new SaleService(app('db'));

        $sale = $service->createSale($user->id, [
            ['medicine_id' => $med->id, 'quantity' => 3, 'unit_price' => 10],
        ], ['type' => 'fixed', 'value' => 5], [
            ['method' => 'cash', 'amount' => 20],
            ['method' => 'mobile', 'amount' => 5],
        ]);

        // subtotal 30 - 5 = 25
        $this->assertEquals(25.0, (float) $sale->total);
        $this->assertCount(2, $sale->payments);
    }
}
