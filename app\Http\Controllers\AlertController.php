<?php

namespace App\Http\Controllers;

use App\Services\AlertService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class AlertController extends Controller
{
    public function __construct(
        private AlertService $alertService
    ) {}

    /**
     * Display the alerts dashboard.
     */
    public function index(Request $request): Response
    {
        $lowStockThreshold = (int) $request->query('low_stock_threshold', 10);
        $expiryDays = (int) $request->query('expiry_days', 30);

        return Inertia::render('alerts/index', [
            'summary' => $this->alertService->getAlertsSummary($lowStockThreshold, $expiryDays),
            'low_stock_medicines' => $this->alertService->getLowStockMedicines($lowStockThreshold),
            'out_of_stock_medicines' => $this->alertService->getOutOfStockMedicines(),
            'expiring_soon_batches' => $this->alertService->getExpiringSoonBatches($expiryDays),
            'expired_batches' => $this->alertService->getExpiredBatches(),
            'filters' => [
                'low_stock_threshold' => $lowStockThreshold,
                'expiry_days' => $expiryDays,
            ],
        ]);
    }

    /**
     * Get alerts summary for API/AJAX requests.
     */
    public function summary(Request $request): JsonResponse
    {
        $lowStockThreshold = (int) $request->query('low_stock_threshold', 10);
        $expiryDays = (int) $request->query('expiry_days', 30);

        return response()->json([
            'summary' => $this->alertService->getAlertsSummary($lowStockThreshold, $expiryDays),
            'critical_alerts' => $this->alertService->getCriticalAlerts(),
        ]);
    }

    /**
     * Get critical alerts for dashboard widget.
     */
    public function critical(): JsonResponse
    {
        return response()->json($this->alertService->getCriticalAlerts());
    }
}
