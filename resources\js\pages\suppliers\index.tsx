import AppLayout from '@/layouts/app-layout'
import { Head, router, usePage } from '@inertiajs/react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { useState } from 'react'

export default function SuppliersIndex() {
  const { props } = usePage()
  const suppliers = (props as any).suppliers
  const filters = (props as any).filters || {}
  const [search, setSearch] = useState(filters.search ?? '')

  function submitSearch(e: React.FormEvent) {
    e.preventDefault()
    router.get('/suppliers', { search }, { preserveState: true, preserveScroll: true })
  }

  function createSupplier() {
    const name = prompt('Supplier name')
    if (!name) return
    router.post('/suppliers', { name })
  }

  function renameSupplier(id: number) {
    const current = suppliers.data.find((s: any) => s.id === id)
    const name = prompt('Rename supplier', current?.name)
    if (!name) return
    router.put(`/suppliers/${id}`, { name })
  }

  function deleteSupplier(id: number) {
    if (!confirm('Delete supplier?')) return
    router.delete(`/suppliers/${id}`)
  }

  return (
    <AppLayout breadcrumbs={[{ title: 'Suppliers', href: '/suppliers' }]}> 
      <Head title="Suppliers" />
      <div className="flex flex-col gap-4 p-4">
        <form onSubmit={submitSearch} className="flex items-center gap-2">
          <Input placeholder="Search..." value={search} onChange={(e) => setSearch(e.target.value)} />
          <Button type="submit">Search</Button>
          <div className="grow" />
          <Button type="button" onClick={createSupplier}>New Supplier</Button>
        </form>

        <div className="rounded-xl border border-sidebar-border/70 dark:border-sidebar-border">
          <table className="w-full text-sm">
            <thead>
              <tr className="text-left">
                <th className="p-3">Name</th>
                <th className="p-3">Email</th>
                <th className="p-3">Phone</th>
                <th className="p-3 w-40">Actions</th>
              </tr>
            </thead>
            <tbody>
              {suppliers.data.map((s: any) => (
                <tr key={s.id} className="border-t border-sidebar-border/70 dark:border-sidebar-border">
                  <td className="p-3">{s.name}</td>
                  <td className="p-3">{s.email ?? '—'}</td>
                  <td className="p-3">{s.phone ?? '—'}</td>
                  <td className="p-3 flex gap-2">
                    <Button variant="secondary" onClick={() => renameSupplier(s.id)}>Edit</Button>
                    <Button variant="destructive" onClick={() => deleteSupplier(s.id)}>Delete</Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="flex items-center gap-2">
          <Button disabled={!suppliers.prev_page_url} onClick={() => router.get(suppliers.prev_page_url)}>Prev</Button>
          <div className="text-sm opacity-70">Page {suppliers.current_page} of {suppliers.last_page}</div>
          <Button disabled={!suppliers.next_page_url} onClick={() => router.get(suppliers.next_page_url)}>Next</Button>
        </div>
      </div>
    </AppLayout>
  )
}

