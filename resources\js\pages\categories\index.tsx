import { CategoryForm, EditCategoryForm } from '@/components/forms';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import { Head, router, usePage } from '@inertiajs/react';
import { useState } from 'react';

export default function CategoriesIndex() {
    const { props } = usePage();
    const categories = (props as any).categories;
    const filters = (props as any).filters || {};
    const [search, setSearch] = useState(filters.search ?? '');

    function submitSearch(e: React.FormEvent) {
        e.preventDefault();
        router.get('/categories', { search }, { preserveState: true, preserveScroll: true });
    }

    function deleteCategory(id: number) {
        if (!confirm('Delete category?')) return;
        router.delete(`/categories/${id}`);
    }

    return (
        <AppLayout breadcrumbs={[{ title: 'Categories', href: '/categories' }]}>
            <Head title="Categories" />
            <div className="flex flex-col gap-4 p-4">
                <form onSubmit={submitSearch} className="flex items-center gap-2">
                    <Input placeholder="Search..." value={search} onChange={(e) => setSearch(e.target.value)} />
                    <Button type="submit">Search</Button>
                    <div className="grow" />
                    <CategoryForm />
                </form>

                <div className="rounded-xl border border-sidebar-border/70 dark:border-sidebar-border">
                    <table className="w-full text-sm">
                        <thead>
                            <tr className="text-left">
                                <th className="p-3">Name</th>
                                <th className="w-40 p-3">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {categories.data.map((c: any) => (
                                <tr key={c.id} className="border-t border-sidebar-border/70 dark:border-sidebar-border">
                                    <td className="p-3">{c.name}</td>
                                    <td className="flex gap-2 p-3">
                                        <EditCategoryForm
                                            category={{ id: c.id, name: c.name }}
                                            trigger={
                                                <Button variant="secondary" size="sm">
                                                    Edit
                                                </Button>
                                            }
                                        />
                                        <Button variant="destructive" size="sm" onClick={() => deleteCategory(c.id)}>
                                            Delete
                                        </Button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {/* Simple pager */}
                <div className="flex items-center gap-2">
                    <Button disabled={!categories.prev_page_url} onClick={() => router.get(categories.prev_page_url)}>
                        Prev
                    </Button>
                    <div className="text-sm opacity-70">
                        Page {categories.current_page} of {categories.last_page}
                    </div>
                    <Button disabled={!categories.next_page_url} onClick={() => router.get(categories.next_page_url)}>
                        Next
                    </Button>
                </div>
            </div>
        </AppLayout>
    );
}
