<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PurchaseItem extends Model
{
    /** @use HasFactory<\Database\Factories\PurchaseItemFactory> */
    use HasFactory;

    protected $fillable = [
        'purchase_id',
        'medicine_id',
        'batch_id',
        'quantity',
        'unit_cost',
        'line_total',
    ];

    /** @return BelongsTo<Purchase, self> */
    public function purchase(): BelongsTo
    {
        return $this->belongsTo(Purchase::class);
    }

    /** @return BelongsTo<Medicine, self> */
    public function medicine(): BelongsTo
    {
        return $this->belongsTo(Medicine::class);
    }
}
