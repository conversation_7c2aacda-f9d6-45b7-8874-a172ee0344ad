import { Card, CardContent } from '@/components/ui/card';
import type { AlertsSummary } from '@/types/alerts';
import { AlertTriangle, Package, PackageX, Timer, XCircle } from 'lucide-react';

interface AlertSummaryCardsProps {
    summary: AlertsSummary;
}

export default function AlertSummaryCards({ summary }: AlertSummaryCardsProps) {
    const summaryCards = [
        {
            title: 'Total Alerts',
            value: summary.total_alerts,
            icon: <AlertTriangle className="h-5 w-5 text-blue-500" />,
            colorClass: '',
        },
        {
            title: 'Out of Stock',
            value: summary.out_of_stock_count,
            icon: <PackageX className="h-5 w-5 text-red-500" />,
            colorClass: 'text-red-600',
        },
        {
            title: 'Low Stock',
            value: summary.low_stock_count,
            icon: <Package className="h-5 w-5 text-yellow-500" />,
            colorClass: 'text-yellow-600',
        },
        {
            title: 'Expired',
            value: summary.expired_count,
            icon: <XCircle className="h-5 w-5 text-red-500" />,
            colorClass: 'text-red-600',
        },
        {
            title: 'Expiring Soon',
            value: summary.expiring_soon_count,
            icon: <Timer className="h-5 w-5 text-orange-500" />,
            colorClass: 'text-orange-600',
        },
    ];

    return (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
            {summaryCards.map((card) => (
                <Card key={card.title}>
                    <CardContent className="p-4">
                        <div className="flex items-center space-x-2">
                            {card.icon}
                            <div>
                                <p className="text-sm font-medium text-muted-foreground">{card.title}</p>
                                <p className={`text-2xl font-bold ${card.colorClass}`}>{card.value}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            ))}
        </div>
    );
}
