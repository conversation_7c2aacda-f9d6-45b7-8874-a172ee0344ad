'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import type { CartItem } from '@/types/pos';
import { Minus, Plus, ShoppingCart as ShoppingCartIcon, X } from 'lucide-react';

interface ShoppingCartProps {
    items: CartItem[];
    subtotal: number;
    onUpdateQuantity: (id: number, quantity: number) => void;
    onRemoveItem: (id: number) => void;
    onClearCart: () => void;
    className?: string;
}

export function ShoppingCart({ items, subtotal, onUpdateQuantity, onRemoveItem, onClearCart, className }: ShoppingCartProps) {
    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(price);
    };

    const handleQuantityChange = (id: number, value: string) => {
        const quantity = parseInt(value) || 1;
        const item = items.find((item) => item.id === id);
        if (item && quantity > 0 && quantity <= item.medicine.stock_quantity) {
            onUpdateQuantity(id, quantity);
        }
    };

    return (
        <Card className={cn('flex h-full flex-col', className)}>
            <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                        <ShoppingCartIcon className="h-5 w-5" />
                        Cart ({items.length})
                    </CardTitle>
                    {items.length > 0 && (
                        <Button variant="ghost" size="sm" onClick={onClearCart} className="text-muted-foreground hover:text-destructive">
                            Clear All
                        </Button>
                    )}
                </div>
            </CardHeader>

            <CardContent className="flex flex-1 flex-col p-0">
                {items.length === 0 ? (
                    <div className="flex flex-1 items-center justify-center p-8 text-center">
                        <div>
                            <ShoppingCartIcon className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                            <p className="text-muted-foreground">Cart is empty</p>
                            <p className="mt-1 text-sm text-muted-foreground">Search and add medicines to get started</p>
                        </div>
                    </div>
                ) : (
                    <>
                        <div className="flex-1 overflow-y-auto px-6">
                            <div className="space-y-3 pb-4">
                                {items.map((item) => (
                                    <div
                                        key={item.id}
                                        className="flex items-center gap-3 rounded-lg border bg-card/50 p-3 transition-colors hover:bg-card"
                                    >
                                        <div className="min-w-0 flex-1">
                                            <div className="mb-1 flex items-center gap-2">
                                                <h4 className="truncate text-sm font-medium">{item.medicine.name}</h4>
                                                <Badge variant="outline" className="shrink-0 text-xs">
                                                    {item.medicine.code}
                                                </Badge>
                                            </div>
                                            <p className="text-xs text-muted-foreground">{formatPrice(item.unit_price)} each</p>
                                        </div>

                                        <div className="flex items-center gap-2">
                                            <Button
                                                variant="outline"
                                                size="icon"
                                                className="h-8 w-8"
                                                onClick={() => onUpdateQuantity(item.id, Math.max(1, item.quantity - 1))}
                                                disabled={item.quantity <= 1}
                                            >
                                                <Minus className="h-3 w-3" />
                                            </Button>

                                            <Input
                                                type="number"
                                                min="1"
                                                max={item.medicine.stock_quantity}
                                                value={item.quantity}
                                                onChange={(e) => handleQuantityChange(item.id, e.target.value)}
                                                className="h-8 w-16 text-center text-sm"
                                            />

                                            <Button
                                                variant="outline"
                                                size="icon"
                                                className="h-8 w-8"
                                                onClick={() => onUpdateQuantity(item.id, Math.min(item.medicine.stock_quantity, item.quantity + 1))}
                                                disabled={item.quantity >= item.medicine.stock_quantity}
                                            >
                                                <Plus className="h-3 w-3" />
                                            </Button>
                                        </div>

                                        <div className="min-w-0 text-right">
                                            <div className="text-sm font-semibold">{formatPrice(item.line_total)}</div>
                                            <Button
                                                variant="ghost"
                                                size="icon"
                                                className="mt-1 h-6 w-6 text-muted-foreground hover:text-destructive"
                                                onClick={() => onRemoveItem(item.id)}
                                            >
                                                <X className="h-3 w-3" />
                                            </Button>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div className="space-y-2 border-t p-6">
                            <div className="flex items-center justify-between text-lg font-semibold">
                                <span>Subtotal:</span>
                                <span>{formatPrice(subtotal)}</span>
                            </div>
                        </div>
                    </>
                )}
            </CardContent>
        </Card>
    );
}
