<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSaleRequest;
use App\Models\Medicine;
use App\Models\Sale;
use App\Services\SaleService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class SaleController extends Controller
{
    public function __construct(
        private SaleService $saleService
    ) {}

    /**
     * Display the POS interface.
     */
    public function index(): Response
    {
        $this->authorize('viewAny', Sale::class);

        return Inertia::render('pos/index', [
            'medicines' => Medicine::query()
                ->with(['category:id,name'])
                ->where('stock_quantity', '>', 0)
                ->orderBy('name')
                ->get(['id', 'name', 'code', 'selling_price', 'stock_quantity', 'category_id']),
        ]);
    }

    /**
     * Search medicines for POS.
     */
    public function search(Request $request): JsonResponse
    {
        $this->authorize('viewAny', Sale::class);

        $search = $request->get('search', '');

        $medicines = Medicine::query()
            ->with(['category:id,name'])
            ->where('stock_quantity', '>', 0)
            ->when($search !== '', function ($q) use ($search) {
                $q->where(function ($q2) use ($search) {
                    $q2->where('name', 'like', "%{$search}%")
                        ->orWhere('code', 'like', "%{$search}%");
                });
            })
            ->orderBy('name')
            ->limit(10)
            ->get(['id', 'name', 'code', 'selling_price', 'stock_quantity', 'category_id']);

        return response()->json($medicines);
    }

    /**
     * Store a newly created sale.
     */
    public function store(StoreSaleRequest $request): RedirectResponse
    {
        $this->authorize('create', Sale::class);

        $data = $request->validated();

        try {
            $sale = $this->saleService->createSale(
                cashierId: Auth::id(),
                items: $data['items'],
                discount: $data['discount'] ?? ['type' => null, 'value' => 0],
                payments: $data['payments'] ?? [['method' => 'cash', 'amount' => $data['total']]]
            );

            return redirect()->route('pos.receipt', $sale)
                ->with('success', 'Sale completed successfully');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Display the sale receipt.
     */
    public function receipt(Sale $sale): Response
    {
        $this->authorize('view', $sale);

        $sale->load(['items.medicine', 'payments', 'cashier']);

        return Inertia::render('pos/receipt', [
            'sale' => $sale,
        ]);
    }
}
