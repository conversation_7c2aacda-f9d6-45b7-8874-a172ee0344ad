<?php

namespace App\Http\Controllers;

use App\Services\ReportsService;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ReportsController extends Controller
{
    public function __construct(
        private ReportsService $reportsService
    ) {}

    /**
     * Display the reports dashboard.
     */
    public function index(Request $request): Response
    {
        // Default to last 30 days
        $startDate = $request->has('start_date')
            ? Carbon::parse($request->start_date)
            : Carbon::now()->subDays(30);

        $endDate = $request->has('end_date')
            ? Carbon::parse($request->end_date)
            : Carbon::now();

        $dashboardData = $this->reportsService->getDashboardData($startDate, $endDate);

        return Inertia::render('reports/index', [
            'data' => $dashboardData,
            'filters' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
            ],
        ]);
    }

    /**
     * Get sales data for API requests.
     */
    public function salesData(Request $request): JsonResponse
    {
        $startDate = Carbon::parse($request->start_date ?? Carbon::now()->subDays(30));
        $endDate = Carbon::parse($request->end_date ?? Carbon::now());

        return response()->json([
            'summary' => $this->reportsService->getSalesSummary($startDate, $endDate),
            'daily_sales' => $this->reportsService->getDailySalesData($startDate, $endDate),
        ]);
    }

    /**
     * Get top selling medicines.
     */
    public function topMedicines(Request $request): JsonResponse
    {
        $startDate = Carbon::parse($request->start_date ?? Carbon::now()->subDays(30));
        $endDate = Carbon::parse($request->end_date ?? Carbon::now());
        $limit = (int) $request->get('limit', 10);

        return response()->json(
            $this->reportsService->getTopSellingMedicines($startDate, $endDate, $limit)
        );
    }
}
