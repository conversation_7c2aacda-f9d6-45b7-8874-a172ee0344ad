import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { getAlertIcon, getStockStatusColor } from '@/lib/alerts-utils';
import type { AlertType, Medicine } from '@/types/alerts';

interface StockAlertTableProps {
    title: string;
    description: string;
    medicines: Medicine[];
    alertType: AlertType;
    threshold?: number;
}

export default function StockAlertTable({ title, description, medicines, alertType, threshold }: StockAlertTableProps) {
    if (medicines.length === 0) {
        return null;
    }

    const stockType = alertType === 'out_of_stock' ? 'out_of_stock' : 'low_stock';

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                    {getAlertIcon(alertType)}
                    <span>{title}</span>
                </CardTitle>
                <CardDescription>
                    {description}
                    {threshold && ` (${threshold} units)`}
                </CardDescription>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Medicine</TableHead>
                            <TableHead>Code</TableHead>
                            <TableHead>Category</TableHead>
                            <TableHead>Supplier</TableHead>
                            <TableHead>Stock</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {medicines.map((medicine) => (
                            <TableRow key={medicine.id}>
                                <TableCell className="font-medium">{medicine.name}</TableCell>
                                <TableCell>{medicine.code}</TableCell>
                                <TableCell>{medicine.category.name}</TableCell>
                                <TableCell>{medicine.supplier.name}</TableCell>
                                <TableCell>
                                    <span
                                        className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${getStockStatusColor(stockType)}`}
                                    >
                                        {medicine.stock_quantity}
                                    </span>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </CardContent>
        </Card>
    );
}
