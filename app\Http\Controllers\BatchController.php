<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreBatchRequest;
use App\Http\Requests\UpdateBatchRequest;
use App\Models\Batch;
use App\Models\Medicine;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class BatchController extends Controller
{
    /**
     * Display a listing of batches for a specific medicine.
     */
    public function index(Request $request, Medicine $medicine): Response
    {
        $this->authorize('viewAny', Batch::class);

        $batches = $medicine->batches()
            ->orderBy('expiry_date')
            ->orderBy('created_at')
            ->paginate(10)
            ->withQueryString();

        return Inertia::render('medicines/batches/index', [
            'medicine' => $medicine,
            'batches' => $batches,
        ]);
    }

    /**
     * Store a newly created batch.
     */
    public function store(StoreBatchRequest $request, Medicine $medicine): RedirectResponse
    {
        $this->authorize('create', Batch::class);

        $medicine->batches()->create($request->validated());

        return redirect()->back()->with('success', 'Batch added successfully');
    }

    /**
     * Update the specified batch.
     */
    public function update(UpdateBatchRequest $request, Medicine $medicine, Batch $batch): RedirectResponse
    {
        $this->authorize('update', $batch);

        // Ensure the batch belongs to the medicine
        if ($batch->medicine_id !== $medicine->id) {
            abort(404);
        }

        $batch->update($request->validated());

        return redirect()->back()->with('success', 'Batch updated successfully');
    }

    /**
     * Remove the specified batch.
     */
    public function destroy(Medicine $medicine, Batch $batch): RedirectResponse
    {
        $this->authorize('delete', $batch);

        // Ensure the batch belongs to the medicine
        if ($batch->medicine_id !== $medicine->id) {
            abort(404);
        }

        $batch->delete();

        return redirect()->back()->with('success', 'Batch deleted successfully');
    }
}
