<?php

namespace App\Services;

use App\Models\Medicine;
use App\Models\Sale;
use App\Models\SaleItem;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class ReportsService
{
    /**
     * Get sales summary for a date range.
     */
    public function getSalesSummary(Carbon $startDate, Carbon $endDate): array
    {
        $sales = Sale::query()
            ->whereBetween('sold_at', [$startDate, $endDate])
            ->get();

        return [
            'total_sales' => $sales->count(),
            'total_revenue' => $sales->sum('total'),
            'average_sale_value' => $sales->count() > 0 ? $sales->avg('total') : 0,
            'total_items_sold' => $sales->sum(function ($sale) {
                return $sale->items->sum('quantity');
            }),
        ];
    }

    /**
     * Get daily sales data for charts.
     */
    public function getDailySalesData(Carbon $startDate, Carbon $endDate): array
    {
        $dailySales = Sale::query()
            ->selectRaw('DATE(sold_at) as date, COUNT(*) as sales_count, SUM(total) as revenue')
            ->whereBetween('sold_at', [$startDate, $endDate])
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $dates = [];
        $salesCounts = [];
        $revenues = [];

        // Fill in missing dates with zero values
        $currentDate = $startDate->copy();
        while ($currentDate <= $endDate) {
            $dateStr = $currentDate->format('Y-m-d');
            $dayData = $dailySales->firstWhere('date', $dateStr);

            $dates[] = $currentDate->format('M d');
            $salesCounts[] = $dayData ? $dayData->sales_count : 0;
            $revenues[] = $dayData ? (float) $dayData->revenue : 0;

            $currentDate->addDay();
        }

        return [
            'dates' => $dates,
            'sales_counts' => $salesCounts,
            'revenues' => $revenues,
        ];
    }

    /**
     * Get top selling medicines.
     */
    public function getTopSellingMedicines(Carbon $startDate, Carbon $endDate, int $limit = 10): array
    {
        return SaleItem::query()
            ->select('medicine_id', DB::raw('SUM(quantity) as total_quantity'), DB::raw('SUM(line_total) as total_revenue'))
            ->whereHas('sale', function ($query) use ($startDate, $endDate) {
                $query->whereBetween('sold_at', [$startDate, $endDate]);
            })
            ->with(['medicine:id,name,code'])
            ->groupBy('medicine_id')
            ->orderBy('total_quantity', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($item) {
                return [
                    'medicine' => $item->medicine,
                    'total_quantity' => $item->total_quantity,
                    'total_revenue' => $item->total_revenue,
                ];
            })
            ->toArray();
    }

    /**
     * Get sales by payment method.
     */
    public function getSalesByPaymentMethod(Carbon $startDate, Carbon $endDate): array
    {
        $paymentData = DB::table('payments')
            ->join('sales', 'payments.sale_id', '=', 'sales.id')
            ->selectRaw('payments.method, COUNT(*) as count, SUM(payments.amount) as total_amount')
            ->whereBetween('sales.sold_at', [$startDate, $endDate])
            ->groupBy('payments.method')
            ->get();

        return $paymentData->map(function ($item) {
            return [
                'method' => ucfirst($item->method),
                'count' => $item->count,
                'total_amount' => (float) $item->total_amount,
            ];
        })->toArray();
    }

    /**
     * Get monthly sales comparison.
     */
    public function getMonthlySalesComparison(int $months = 6): array
    {
        $monthlySales = Sale::query()
            ->selectRaw('YEAR(sold_at) as year, MONTH(sold_at) as month, COUNT(*) as sales_count, SUM(total) as revenue')
            ->where('sold_at', '>=', Carbon::now()->subMonths($months))
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        $months = [];
        $salesCounts = [];
        $revenues = [];

        foreach ($monthlySales as $monthData) {
            $months[] = Carbon::createFromDate($monthData->year, $monthData->month, 1)->format('M Y');
            $salesCounts[] = $monthData->sales_count;
            $revenues[] = (float) $monthData->revenue;
        }

        return [
            'months' => $months,
            'sales_counts' => $salesCounts,
            'revenues' => $revenues,
        ];
    }

    /**
     * Get low stock medicines count.
     */
    public function getLowStockCount(int $threshold = 10): int
    {
        return Medicine::query()
            ->where('stock_quantity', '<=', $threshold)
            ->where('stock_quantity', '>', 0)
            ->count();
    }

    /**
     * Get comprehensive dashboard data.
     */
    public function getDashboardData(Carbon $startDate, Carbon $endDate): array
    {
        return [
            'summary' => $this->getSalesSummary($startDate, $endDate),
            'daily_sales' => $this->getDailySalesData($startDate, $endDate),
            'top_medicines' => $this->getTopSellingMedicines($startDate, $endDate, 5),
            'payment_methods' => $this->getSalesByPaymentMethod($startDate, $endDate),
            'monthly_comparison' => $this->getMonthlySalesComparison(6),
            'low_stock_count' => $this->getLowStockCount(),
        ];
    }
}
