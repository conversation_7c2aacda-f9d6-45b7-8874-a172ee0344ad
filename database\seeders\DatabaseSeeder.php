<?php

namespace Database\Seeders;

use App\Models\User;
use App\Role;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        User::factory()->create([
            'name' => 'Owner Admin',
            'email' => '<EMAIL>',
            'role' => Role::Admin,
        ]);

        User::factory()->create([
            'name' => 'Cashier',
            'email' => '<EMAIL>',
            'role' => Role::Cashier,
        ]);
    }
}
