import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { getAlertIcon, getDaysUntilExpiry, getExpiryStatusColor } from '@/lib/alerts-utils';
import type { AlertType, Batch } from '@/types/alerts';
import { format } from 'date-fns';

interface ExpiryAlertTableProps {
    title: string;
    description: string;
    batches: Batch[];
    alertType: AlertType;
    expiryDays?: number;
}

export default function ExpiryAlertTable({ title, description, batches, alertType, expiryDays }: ExpiryAlertTableProps) {
    if (batches.length === 0) {
        return null;
    }

    const expiryType = alertType === 'expired' ? 'expired' : 'expiring';

    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                    {getAlertIcon(alertType)}
                    <span>{title}</span>
                </CardTitle>
                <CardDescription>
                    {description}
                    {expiryDays && ` ${expiryDays} days`}
                </CardDescription>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Medicine</TableHead>
                            <TableHead>Batch Number</TableHead>
                            <TableHead>Expiry Date</TableHead>
                            <TableHead>Quantity</TableHead>
                            <TableHead>{alertType === 'expired' ? 'Days Expired' : 'Days Until Expiry'}</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {batches.map((batch) => {
                            const daysUntilExpiry = getDaysUntilExpiry(batch.expiry_date);
                            const displayDays = alertType === 'expired' ? Math.abs(daysUntilExpiry) : daysUntilExpiry;

                            return (
                                <TableRow key={batch.id}>
                                    <TableCell className="font-medium">
                                        {batch.medicine.name}
                                        <p className="text-sm text-muted-foreground">{batch.medicine.code}</p>
                                    </TableCell>
                                    <TableCell>{batch.batch_number}</TableCell>
                                    <TableCell>{format(new Date(batch.expiry_date), 'MMM dd, yyyy')}</TableCell>
                                    <TableCell>{batch.quantity}</TableCell>
                                    <TableCell>
                                        <span
                                            className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${getExpiryStatusColor(expiryType)}`}
                                        >
                                            {displayDays} days
                                        </span>
                                    </TableCell>
                                </TableRow>
                            );
                        })}
                    </TableBody>
                </Table>
            </CardContent>
        </Card>
    );
}
