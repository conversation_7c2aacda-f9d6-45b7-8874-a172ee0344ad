import { DateRangeFilter, SummaryCards, TopMedicinesTable } from '@/components/reports';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import type { ReportsData, ReportsFilters } from '@/types/reports';
import { Head } from '@inertiajs/react';
import { useMemo } from 'react';

interface ReportsIndexProps {
    data: ReportsData;
    filters: ReportsFilters;
}

export default function ReportsIndex({ data, filters }: ReportsIndexProps) {
    const breadcrumbs: BreadcrumbItem[] = [{ title: 'Reports', href: '#' }];

    const formatCurrency = useMemo(
        () => (amount: number) => {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
            }).format(amount);
        },
        [],
    );

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Sales Reports & Analytics" />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold">Sales Reports & Analytics</h1>
                        <p className="text-muted-foreground">Comprehensive sales analysis and business insights</p>
                    </div>
                </div>

                {/* Date Range Filter */}
                <DateRangeFilter filters={filters} />

                {/* Summary Cards */}
                <SummaryCards summary={data.summary} formatCurrency={formatCurrency} />

                {/* Top Medicines Table */}
                <TopMedicinesTable topMedicines={data.top_medicines} formatCurrency={formatCurrency} />
            </div>
        </AppLayout>
    );
}
