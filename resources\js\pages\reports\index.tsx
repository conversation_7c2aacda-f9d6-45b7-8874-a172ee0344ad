import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router } from '@inertiajs/react';
import { format } from 'date-fns';
import { BarChart3, DollarSign, Package, ShoppingCart, TrendingUp } from 'lucide-react';
import { useState } from 'react';
import Chart from 'react-apexcharts';

interface Medicine {
    id: number;
    name: string;
    code: string;
}

interface TopMedicine {
    medicine: Medicine;
    total_quantity: number;
    total_revenue: number;
}

interface PaymentMethod {
    method: string;
    count: number;
    total_amount: number;
}

interface Summary {
    total_sales: number;
    total_revenue: number;
    average_sale_value: number;
    total_items_sold: number;
}

interface DailySales {
    dates: string[];
    sales_counts: number[];
    revenues: number[];
}

interface MonthlySales {
    months: string[];
    sales_counts: number[];
    revenues: number[];
}

interface ReportsData {
    summary: Summary;
    daily_sales: DailySales;
    top_medicines: TopMedicine[];
    payment_methods: PaymentMethod[];
    monthly_comparison: MonthlySales;
    low_stock_count: number;
}

interface ReportsIndexProps {
    data: ReportsData;
    filters: {
        start_date: string;
        end_date: string;
    };
}

export default function ReportsIndex({ data, filters }: ReportsIndexProps) {
    const [startDate, setStartDate] = useState(filters.start_date);
    const [endDate, setEndDate] = useState(filters.end_date);

    const breadcrumbs: BreadcrumbItem[] = [
        { title: 'Reports', href: '#' },
    ];

    const handleFilterUpdate = () => {
        router.get(window.location.pathname, {
            start_date: startDate,
            end_date: endDate,
        }, {
            preserveState: true,
            replace: true,
        });
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(amount);
    };

    // Chart configurations
    const dailySalesChartOptions = {
        chart: {
            type: 'line' as const,
            height: 350,
            toolbar: {
                show: false,
            },
        },
        stroke: {
            curve: 'smooth' as const,
            width: 2,
        },
        xaxis: {
            categories: data.daily_sales.dates,
        },
        yaxis: [
            {
                title: {
                    text: 'Sales Count',
                },
            },
            {
                opposite: true,
                title: {
                    text: 'Revenue ($)',
                },
            },
        ],
        colors: ['#3B82F6', '#10B981'],
        legend: {
            position: 'top' as const,
        },
    };

    const dailySalesChartSeries = [
        {
            name: 'Sales Count',
            type: 'line',
            data: data.daily_sales.sales_counts,
        },
        {
            name: 'Revenue',
            type: 'line',
            yAxisIndex: 1,
            data: data.daily_sales.revenues,
        },
    ];

    const paymentMethodsChartOptions = {
        chart: {
            type: 'donut' as const,
            height: 350,
        },
        labels: data.payment_methods.map(pm => pm.method),
        colors: ['#3B82F6', '#10B981', '#F59E0B'],
        legend: {
            position: 'bottom' as const,
        },
    };

    const paymentMethodsChartSeries = data.payment_methods.map(pm => pm.total_amount);

    const monthlyComparisonChartOptions = {
        chart: {
            type: 'bar' as const,
            height: 350,
            toolbar: {
                show: false,
            },
        },
        xaxis: {
            categories: data.monthly_comparison.months,
        },
        yaxis: [
            {
                title: {
                    text: 'Sales Count',
                },
            },
            {
                opposite: true,
                title: {
                    text: 'Revenue ($)',
                },
            },
        ],
        colors: ['#8B5CF6', '#EF4444'],
        legend: {
            position: 'top' as const,
        },
    };

    const monthlyComparisonChartSeries = [
        {
            name: 'Sales Count',
            data: data.monthly_comparison.sales_counts,
        },
        {
            name: 'Revenue',
            data: data.monthly_comparison.revenues,
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Sales Reports & Analytics" />
            
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold">Sales Reports & Analytics</h1>
                        <p className="text-muted-foreground">
                            Comprehensive sales analysis and business insights
                        </p>
                    </div>
                </div>

                {/* Date Range Filter */}
                <Card>
                    <CardHeader>
                        <CardTitle>Report Period</CardTitle>
                        <CardDescription>
                            Select date range for analysis
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-end space-x-4">
                            <div>
                                <Label htmlFor="start_date">Start Date</Label>
                                <Input
                                    id="start_date"
                                    type="date"
                                    value={startDate}
                                    onChange={(e) => setStartDate(e.target.value)}
                                />
                            </div>
                            <div>
                                <Label htmlFor="end_date">End Date</Label>
                                <Input
                                    id="end_date"
                                    type="date"
                                    value={endDate}
                                    onChange={(e) => setEndDate(e.target.value)}
                                />
                            </div>
                            <Button onClick={handleFilterUpdate}>
                                Update Report
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <ShoppingCart className="h-5 w-5 text-blue-500" />
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Sales</p>
                                    <p className="text-2xl font-bold">{data.summary.total_sales}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <DollarSign className="h-5 w-5 text-green-500" />
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Total Revenue</p>
                                    <p className="text-2xl font-bold text-green-600">
                                        {formatCurrency(data.summary.total_revenue)}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <TrendingUp className="h-5 w-5 text-purple-500" />
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Avg Sale Value</p>
                                    <p className="text-2xl font-bold text-purple-600">
                                        {formatCurrency(data.summary.average_sale_value)}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center space-x-2">
                                <Package className="h-5 w-5 text-orange-500" />
                                <div>
                                    <p className="text-sm font-medium text-muted-foreground">Items Sold</p>
                                    <p className="text-2xl font-bold text-orange-600">
                                        {data.summary.total_items_sold}
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Charts Row 1 */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center space-x-2">
                                <BarChart3 className="h-5 w-5" />
                                <span>Daily Sales Trend</span>
                            </CardTitle>
                            <CardDescription>
                                Sales count and revenue over time
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Chart
                                options={dailySalesChartOptions}
                                series={dailySalesChartSeries}
                                type="line"
                                height={350}
                            />
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Payment Methods Distribution</CardTitle>
                            <CardDescription>
                                Revenue breakdown by payment method
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Chart
                                options={paymentMethodsChartOptions}
                                series={paymentMethodsChartSeries}
                                type="donut"
                                height={350}
                            />
                        </CardContent>
                    </Card>
                </div>

                {/* Charts Row 2 */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Monthly Comparison</CardTitle>
                            <CardDescription>
                                Sales performance over recent months
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <Chart
                                options={monthlyComparisonChartOptions}
                                series={monthlyComparisonChartSeries}
                                type="bar"
                                height={350}
                            />
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Top Selling Medicines</CardTitle>
                            <CardDescription>
                                Best performing products by quantity sold
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            {data.top_medicines.length > 0 ? (
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Medicine</TableHead>
                                            <TableHead>Quantity</TableHead>
                                            <TableHead>Revenue</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {data.top_medicines.map((item, index) => (
                                            <TableRow key={item.medicine.id}>
                                                <TableCell>
                                                    <div>
                                                        <p className="font-medium">{item.medicine.name}</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {item.medicine.code}
                                                        </p>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                        {item.total_quantity}
                                                    </span>
                                                </TableCell>
                                                <TableCell className="font-medium">
                                                    {formatCurrency(item.total_revenue)}
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            ) : (
                                <div className="text-center py-8 text-muted-foreground">
                                    No sales data available for the selected period
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
