[ ] NAME:Current Task List DESCRIPTION:Root task for conversation e723b2dc-882f-47cd-81bf-31d3e58e25d4
-[x] NAME:Investigate/Triage/Understand the problem DESCRIPTION:Read requirements.txt and scan project metadata (composer.json, package.json, README) to confirm stack (Laravel 12, Inertia React, Tailwind 4) and note any preinstalled libraries like ApexCharts.
-[x] NAME:Product/Medicine CRUD (Inertia) DESCRIPTION:Build Category, Medicine, and Supplier CRUD controllers and Inertia pages with validation via Form Requests. Include search, pagination, and basic UI matching existing layout.
-[/] NAME:Product/Medicine CRUD (Inertia) UI DESCRIPTION:Controllers, Form Requests, Inertia pages for Category, Medicine, Supplier with search, sort, pagination. Batch subpage to add batches with expiry/qty.
-[x] NAME:Roles and authorization DESCRIPTION:Introduce a Role enum and add a role column to users. Update User model casts and helpers. Seed admin and cashier users. (Route gating and nav visibility are handled in the separate 'Security, roles, and navigation updates' task.)
-[ ] NAME:Security, roles, and navigation updates DESCRIPTION:Policies/gates for admin-only areas; hide/show nav items based on role. Feature tests.
-[x] NAME:Core data model: categories, medicines, suppliers, batches DESCRIPTION:Create Eloquent models, migrations, and factories for Category, Medicine, Supplier, and Batch with relationships. Do not run migrations yet. Add basic unit tests for relationships.
-[x] NAME:POS (Sales) domain models and stock deduction DESCRIPTION:Create models/migrations: Sale, SaleItem, Payment. Implement stock deduction service using batches (FIFO), discounts (percent/fixed), multi-payment with cash default. Unit tests for stock and totals. Do not build UI yet.
-[x] NAME:Suppliers purchases and stock increases DESCRIPTION:Create Purchase and PurchaseItem models/migrations. Record supplier purchases (invoice no, date, cost). Increase stock via stock movement service. Feature tests.
-[x] NAME:Inventory tracking and alerts DESCRIPTION:Implement low-stock thresholds per medicine and near-expiry detection. Schedule notifications. Add database indices for expiry queries. Tests for alerts selection logic.
-[ ] NAME:POS (Sales Screen) UI DESCRIPTION:Build Inertia React POS screen: search by name/code, cart, discounts, payments, submit sale, print receipt. Optimistic UI and keyboard shortcuts.
-[ ] NAME:Sales reports and dashboard (ApexCharts) DESCRIPTION:Daily/weekly/monthly sales, profit, best-sellers. Implement API endpoints and Inertia pages. Use apexcharts to render charts on dashboard and reports pages. Add empty-state skeletons with deferred props.
-[ ] NAME:Customers (Optional) DESCRIPTION:Customer model and attach to sales. Simple CRUD and assignment from POS. History page.
-[ ] NAME:Returns & refunds (Nice-to-have) DESCRIPTION:Return model linked to SaleItem. Adjust stock and totals; authorization flow.
-[ ] NAME:Credit/debt management (Nice-to-have) DESCRIPTION:Allow sales on credit with customer balances and payments against balances. Reports.
-[ ] NAME:Shift sessions & cash closing (Nice-to-have) DESCRIPTION:Shift model per cashier with opening/closing amounts and end-of-day report.
-[ ] NAME:Expense tracking (Nice-to-have) DESCRIPTION:Expense model and UI. Include in profit calc on reports.
-[ ] NAME:Backups and activity logs DESCRIPTION:Schedule database backups and implement basic activity logging for sales and inventory changes.