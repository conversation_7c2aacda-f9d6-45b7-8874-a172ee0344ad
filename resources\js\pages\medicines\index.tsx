import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import { type SharedData } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { useState } from 'react';

export default function MedicinesIndex() {
    const { props, auth } = usePage<SharedData>();
    const medicines = (props as any).medicines;
    const isAdmin = auth.user?.role === 'admin';
    const filters = (props as any).filters || {};
    const [search, setSearch] = useState(filters.search ?? '');

    function submitSearch(e: React.FormEvent) {
        e.preventDefault();
        router.get('/medicines', { search }, { preserveState: true, preserveScroll: true });
    }

    function createMedicine() {
        const name = prompt('Medicine name');
        const code = name ? name.replace(/\s+/g, '-').toUpperCase().slice(0, 20) : '';
        if (!name) return;
        router.post('/medicines', {
            name,
            code,
            category_id: (props as any).categories?.[0]?.id ?? 1,
            stock_quantity: 0,
            purchase_price: 0,
            selling_price: 0,
        });
    }

    function deleteMedicine(id: number) {
        if (!confirm('Delete medicine?')) return;
        router.delete(`/medicines/${id}`);
    }

    return (
        <AppLayout breadcrumbs={[{ title: 'Medicines', href: '/medicines' }]}>
            <Head title="Medicines" />
            <div className="flex flex-col gap-4 p-4">
                <form onSubmit={submitSearch} className="flex items-center gap-2">
                    <Input placeholder="Search name/code..." value={search} onChange={(e) => setSearch(e.target.value)} />
                    <Button type="submit">Search</Button>
                    <div className="grow" />
                    <Button type="button" onClick={createMedicine}>
                        Quick Add
                    </Button>
                </form>

                <div className="overflow-x-auto rounded-xl border border-sidebar-border/70 dark:border-sidebar-border">
                    <table className="w-full text-sm">
                        <thead>
                            <tr className="text-left whitespace-nowrap">
                                <th className="p-3">Name</th>
                                <th className="p-3">Code</th>
                                <th className="p-3">Category</th>
                                <th className="p-3">Supplier</th>
                                <th className="p-3">Stock</th>
                                <th className="p-3">Purchase</th>
                                <th className="p-3">Price</th>
                                <th className="w-40 p-3">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {medicines.data.map((m: any) => (
                                <tr key={m.id} className="border-t border-sidebar-border/70 dark:border-sidebar-border">
                                    <td className="p-3">{m.name}</td>
                                    <td className="p-3">{m.code}</td>
                                    <td className="p-3">{m.category?.name ?? '—'}</td>
                                    <td className="p-3">{m.supplier?.name ?? '—'}</td>
                                    <td className="p-3">{m.stock_quantity}</td>
                                    <td className="p-3">{m.purchase_price}</td>
                                    <td className="p-3">{m.selling_price}</td>
                                    <td className="p-3">
                                        <div className="flex gap-2">
                                            <Link href={`/medicines/${m.id}/batches`}>
                                                <Button variant="outline" size="sm">
                                                    Batches
                                                </Button>
                                            </Link>
                                            {isAdmin && (
                                                <Button variant="destructive" size="sm" onClick={() => deleteMedicine(m.id)}>
                                                    Delete
                                                </Button>
                                            )}
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                <div className="flex items-center gap-2">
                    <Button disabled={!medicines.prev_page_url} onClick={() => router.get(medicines.prev_page_url)}>
                        Prev
                    </Button>
                    <div className="text-sm opacity-70">
                        Page {medicines.current_page} of {medicines.last_page}
                    </div>
                    <Button disabled={!medicines.next_page_url} onClick={() => router.get(medicines.next_page_url)}>
                        Next
                    </Button>
                </div>
            </div>
        </AppLayout>
    );
}
