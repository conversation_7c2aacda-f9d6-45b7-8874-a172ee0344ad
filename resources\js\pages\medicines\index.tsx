import { MedicineFiltersComponent, type MedicineFilters } from '@/components/medicines/medicine-filters';
import { MedicinesTable } from '@/components/medicines/medicines-table';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import { type SharedData } from '@/types';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Download, Package, Plus, Upload } from 'lucide-react';
import { useMemo, useRef, useState } from 'react';

export default function MedicinesIndex() {
    const { props } = usePage<SharedData>();
    const { auth } = props;
    const medicines = (props as any).medicines?.data || [];
    const categories = (props as any).categories || [];
    const suppliers = (props as any).suppliers || [];

    const [filters, setFilters] = useState<MedicineFilters>({
        search: '',
        category_id: null,
        supplier_id: null,
        stock_status: 'all',
        min_price: undefined,
        max_price: undefined,
    });

    const isAdmin = auth.user?.role === 'admin';
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleExport = () => {
        window.location.href = '/medicines/export';
    };

    const handleDownloadTemplate = () => {
        window.location.href = '/medicines/template';
    };

    const handleImportClick = () => {
        fileInputRef.current?.click();
    };

    const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            const formData = new FormData();
            formData.append('file', file);

            router.post('/medicines/import', formData, {
                forceFormData: true,
                onSuccess: () => {
                    // Reset file input
                    if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                    }
                },
                onError: (errors) => {
                    console.error('Import errors:', errors);
                    // Reset file input
                    if (fileInputRef.current) {
                        fileInputRef.current.value = '';
                    }
                },
            });
        }
    };

    // Filter medicines based on current filters
    const filteredMedicines = useMemo(() => {
        return medicines.filter((medicine: any) => {
            // Search filter
            if (filters.search) {
                const searchTerm = filters.search.toLowerCase();
                const matchesSearch =
                    medicine.name.toLowerCase().includes(searchTerm) ||
                    medicine.code.toLowerCase().includes(searchTerm) ||
                    (medicine.description && medicine.description.toLowerCase().includes(searchTerm));

                if (!matchesSearch) return false;
            }

            // Category filter
            if (filters.category_id && medicine.category_id !== filters.category_id) {
                return false;
            }

            // Supplier filter
            if (filters.supplier_id && medicine.supplier_id !== filters.supplier_id) {
                return false;
            }

            // Stock status filter
            if (filters.stock_status && filters.stock_status !== 'all') {
                const stockQuantity = medicine.stock_quantity;
                const minStockLevel = medicine.min_stock_level || 10;

                switch (filters.stock_status) {
                    case 'out_of_stock':
                        if (stockQuantity > 0) return false;
                        break;
                    case 'low_stock':
                        if (stockQuantity === 0 || stockQuantity > minStockLevel) return false;
                        break;
                    case 'in_stock':
                        if (stockQuantity <= minStockLevel) return false;
                        break;
                }
            }

            // Price range filter
            if (filters.min_price && medicine.selling_price < filters.min_price) {
                return false;
            }
            if (filters.max_price && medicine.selling_price > filters.max_price) {
                return false;
            }

            return true;
        });
    }, [medicines, filters]);

    // Calculate stock statistics
    const stockStats = useMemo(() => {
        const total = medicines.length;
        const outOfStock = medicines.filter((m: any) => m.stock_quantity === 0).length;
        const lowStock = medicines.filter((m: any) => m.stock_quantity > 0 && m.stock_quantity <= (m.min_stock_level || 10)).length;
        const inStock = total - outOfStock - lowStock;

        return { total, inStock, lowStock, outOfStock };
    }, [medicines]);

    const handleDelete = (id: number) => {
        router.delete(`/medicines/${id}`);
    };

    return (
        <AppLayout breadcrumbs={[{ title: 'Medicines', href: '/medicines' }]}>
            <Head title="Medicines" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Medicine Inventory</h1>
                        <p className="text-muted-foreground">Manage your pharmacy's medicine inventory and stock levels</p>
                    </div>

                    <div className="flex gap-2">
                        <Button variant="outline" size="sm" onClick={handleExport}>
                            <Download className="mr-2 h-4 w-4" />
                            Export
                        </Button>
                        {isAdmin && (
                            <>
                                <div className="flex gap-1">
                                    <Button variant="outline" size="sm" onClick={handleImportClick}>
                                        <Upload className="mr-2 h-4 w-4" />
                                        Import
                                    </Button>
                                    <Button variant="outline" size="sm" onClick={handleDownloadTemplate} title="Download Import Template">
                                        <Download className="h-4 w-4" />
                                    </Button>
                                </div>
                                <Input ref={fileInputRef} type="file" accept=".csv,.txt" onChange={handleFileSelect} className="hidden" />
                                <Button asChild>
                                    <Link href="/medicines/create">
                                        <Plus className="mr-2 h-4 w-4" />
                                        Add Medicine
                                    </Link>
                                </Button>
                            </>
                        )}
                    </div>
                </div>

                {/* Stock Statistics */}
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Medicines</CardTitle>
                            <Package className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stockStats.total}</div>
                            <p className="text-xs text-muted-foreground">Active medicine varieties</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">In Stock</CardTitle>
                            <div className="h-4 w-4 rounded-full bg-green-500"></div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{stockStats.inStock}</div>
                            <p className="text-xs text-muted-foreground">Above minimum levels</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Low Stock</CardTitle>
                            <div className="h-4 w-4 rounded-full bg-orange-500"></div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-600">{stockStats.lowStock}</div>
                            <p className="text-xs text-muted-foreground">Need reordering</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
                            <div className="h-4 w-4 rounded-full bg-red-500"></div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-red-600">{stockStats.outOfStock}</div>
                            <p className="text-xs text-muted-foreground">Urgent attention needed</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Filters */}
                <MedicineFiltersComponent filters={filters} onFiltersChange={setFilters} categories={categories} suppliers={suppliers} />

                {/* Medicines Table */}
                <MedicinesTable medicines={filteredMedicines} onDelete={handleDelete} isAdmin={isAdmin} />
            </div>
        </AppLayout>
    );
}
