<?php

namespace Tests\Feature;

use App\Models\Batch;
use App\Models\Category;
use App\Models\Medicine;
use App\Models\Supplier;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class InventoryAlertsTest extends TestCase
{
    use RefreshDatabase;

    public function test_alerts_command_lists_low_stock_and_near_expiry(): void
    {
        $category = Category::factory()->create();
        $supplier = Supplier::factory()->create();
        $m = Medicine::factory()->create([
            'category_id' => $category->id,
            'supplier_id' => $supplier->id,
            'stock_quantity' => 5,
        ]);

        Batch::factory()->create([
            'medicine_id' => $m->id,
            'quantity' => 3,
            'expiry_date' => now()->addWeeks(2)->toDateString(),
        ]);

        $this->artisan('inventory:alerts')
            ->expectsOutputToContain('LOW STOCK:')
            ->expectsOutputToContain('NEAR EXPIRY:')
            ->assertExitCode(0);
    }
}
