export interface Medicine {
    id: number;
    name: string;
    code: string;
}

export interface TopMedicine {
    medicine: Medicine;
    total_quantity: number;
    total_revenue: number;
}

export interface PaymentMethod {
    method: string;
    count: number;
    total_amount: number;
}

export interface Summary {
    total_sales: number;
    total_revenue: number;
    average_sale_value: number;
    total_items_sold: number;
}

export interface DailySales {
    dates: string[];
    sales_counts: number[];
    revenues: number[];
}

export interface MonthlySales {
    months: string[];
    sales_counts: number[];
    revenues: number[];
}

export interface ReportsData {
    summary: Summary;
    daily_sales: DailySales;
    monthly_comparison: MonthlySales;
    payment_methods: PaymentMethod[];
    top_medicines: TopMedicine[];
}

export interface ReportsFilters {
    start_date: string;
    end_date: string;
}
