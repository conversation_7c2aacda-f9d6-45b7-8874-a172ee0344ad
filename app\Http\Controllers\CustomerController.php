<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreCustomerRequest;
use App\Http\Requests\UpdateCustomerRequest;
use App\Models\Customer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class CustomerController extends Controller
{
    /**
     * Display a listing of customers.
     */
    public function index(Request $request): Response
    {
        $search = $request->get('search', '');

        $customers = Customer::query()
            ->when($search !== '', function ($q) use ($search) {
                $q->where(function ($q2) use ($search) {
                    $q2->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%")
                        ->orWhere('phone', 'like', "%{$search}%");
                });
            })
            ->withCount('sales')
            ->withSum('sales', 'total')
            ->orderBy('name')
            ->paginate(15)
            ->withQueryString();

        return Inertia::render('customers/index', [
            'customers' => $customers,
            'filters' => ['search' => $search],
        ]);
    }

    /**
     * Store a newly created customer.
     */
    public function store(StoreCustomerRequest $request): RedirectResponse
    {
        Customer::create($request->validated());

        return redirect()->back()->with('success', 'Customer created successfully');
    }

    /**
     * Display the specified customer.
     */
    public function show(Customer $customer): Response
    {
        $customer->load(['sales.items.medicine', 'sales.payments']);

        return Inertia::render('customers/show', [
            'customer' => $customer,
            'sales_history' => $customer->sales()
                ->with(['items.medicine', 'payments', 'cashier'])
                ->orderBy('sold_at', 'desc')
                ->paginate(10),
        ]);
    }

    /**
     * Update the specified customer.
     */
    public function update(UpdateCustomerRequest $request, Customer $customer): RedirectResponse
    {
        $customer->update($request->validated());

        return redirect()->back()->with('success', 'Customer updated successfully');
    }

    /**
     * Remove the specified customer.
     */
    public function destroy(Customer $customer): RedirectResponse
    {
        // Check if customer has sales
        if ($customer->sales()->exists()) {
            return redirect()->back()->withErrors([
                'error' => 'Cannot delete customer with existing sales records.'
            ]);
        }

        $customer->delete();

        return redirect()->back()->with('success', 'Customer deleted successfully');
    }

    /**
     * Search customers for API/AJAX requests.
     */
    public function search(Request $request): JsonResponse
    {
        $search = $request->get('search', '');

        $customers = Customer::query()
            ->when($search !== '', function ($q) use ($search) {
                $q->where(function ($q2) use ($search) {
                    $q2->where('name', 'like', "%{$search}%")
                        ->orWhere('phone', 'like', "%{$search}%");
                });
            })
            ->orderBy('name')
            ->limit(10)
            ->get(['id', 'name', 'phone', 'email']);

        return response()->json($customers);
    }
}
