export interface Category {
    id: number;
    name: string;
}

export interface Supplier {
    id: number;
    name: string;
}

export interface Medicine {
    id: number;
    name: string;
    code: string;
    stock_quantity: number;
    category: Category;
    supplier: Supplier;
}

export interface Batch {
    id: number;
    batch_number: string;
    expiry_date: string;
    quantity: number;
    medicine: {
        id: number;
        name: string;
        code: string;
        category: Category;
    };
}

export interface AlertsSummary {
    low_stock_count: number;
    out_of_stock_count: number;
    expiring_soon_count: number;
    expired_count: number;
    total_alerts: number;
}

export interface AlertFilters {
    low_stock_threshold: number;
    expiry_days: number;
}

export interface AlertsData {
    summary: AlertsSummary;
    low_stock_medicines: Medicine[];
    out_of_stock_medicines: Medicine[];
    expiring_soon_batches: Batch[];
    expired_batches: Batch[];
    filters: AlertFilters;
}

export type AlertType = 'out_of_stock' | 'low_stock' | 'expired' | 'expiring';
