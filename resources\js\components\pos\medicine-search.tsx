'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import type { Medicine } from '@/types/pos';
import { Package, Search } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';

interface MedicineSearchProps {
    medicines: Medicine[];
    onAddToCart: (medicine: Medicine) => void;
    className?: string;
}

export function MedicineSearch({ medicines, onAddToCart, className }: MedicineSearchProps) {
    const [search, setSearch] = useState('');
    const [filteredMedicines, setFilteredMedicines] = useState<Medicine[]>([]);
    const [isOpen, setIsOpen] = useState(false);
    const [selectedIndex, setSelectedIndex] = useState(0);
    const searchRef = useRef<HTMLInputElement>(null);
    const listRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        if (search.trim()) {
            const filtered = medicines
                .filter(
                    (medicine) =>
                        medicine.name.toLowerCase().includes(search.toLowerCase()) ||
                        medicine.code.toLowerCase().includes(search.toLowerCase()) ||
                        medicine.category.name.toLowerCase().includes(search.toLowerCase()),
                )
                .slice(0, 8);
            setFilteredMedicines(filtered);
            setIsOpen(filtered.length > 0);
            setSelectedIndex(0);
        } else {
            setFilteredMedicines([]);
            setIsOpen(false);
        }
    }, [search, medicines]);

    const handleKeyDown = (e: React.KeyboardEvent) => {
        if (!isOpen) return;

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                setSelectedIndex((prev) => Math.min(prev + 1, filteredMedicines.length - 1));
                break;
            case 'ArrowUp':
                e.preventDefault();
                setSelectedIndex((prev) => Math.max(prev - 1, 0));
                break;
            case 'Enter':
                e.preventDefault();
                if (filteredMedicines[selectedIndex]) {
                    handleSelectMedicine(filteredMedicines[selectedIndex]);
                }
                break;
            case 'Escape':
                setIsOpen(false);
                break;
        }
    };

    const handleSelectMedicine = (medicine: Medicine) => {
        onAddToCart(medicine);
        setSearch('');
        setIsOpen(false);
        searchRef.current?.focus();
    };

    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(price);
    };

    return (
        <div className={cn('relative', className)}>
            <div className="relative">
                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-muted-foreground" />
                <Input
                    ref={searchRef}
                    placeholder="Search medicines by name, code, or category..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    onKeyDown={handleKeyDown}
                    className="py-3 pr-4 pl-10 text-base"
                    autoComplete="off"
                />
            </div>

            {isOpen && (
                <Card className="absolute top-full right-0 left-0 z-50 mt-1 max-h-96 overflow-hidden shadow-lg">
                    <CardContent className="p-0">
                        <div ref={listRef} className="max-h-96 overflow-y-auto">
                            {filteredMedicines.map((medicine, index) => (
                                <div
                                    key={medicine.id}
                                    className={cn(
                                        'flex cursor-pointer items-center justify-between border-b border-border/50 p-3 transition-colors last:border-0',
                                        index === selectedIndex ? 'bg-accent text-accent-foreground' : 'hover:bg-muted/50',
                                    )}
                                    onClick={() => handleSelectMedicine(medicine)}
                                >
                                    <div className="min-w-0 flex-1">
                                        <div className="mb-1 flex items-center gap-2">
                                            <Package className="h-4 w-4 shrink-0 text-muted-foreground" />
                                            <h4 className="truncate text-sm font-medium">{medicine.name}</h4>
                                            <Badge variant="outline" className="shrink-0 text-xs">
                                                {medicine.code}
                                            </Badge>
                                        </div>
                                        <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                            <span>Category: {medicine.category.name}</span>
                                            <span>Stock: {medicine.stock_quantity}</span>
                                        </div>
                                    </div>
                                    <div className="ml-3 shrink-0 text-right">
                                        <div className="font-semibold text-primary">{formatPrice(medicine.selling_price)}</div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            )}
        </div>
    );
}
