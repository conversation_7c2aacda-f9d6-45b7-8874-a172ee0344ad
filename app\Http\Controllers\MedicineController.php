<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreMedicineRequest;
use App\Http\Requests\UpdateMedicineRequest;
use App\Models\Medicine;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class MedicineController extends Controller
{
    public function index(Request $request): Response
    {
        $this->authorize('viewAny', Medicine::class);

        $search = (string) $request->query('search', '');
        $medicines = Medicine::query()
            ->with(['category:id,name', 'supplier:id,name'])
            ->when($search !== '', function ($q) use ($search) {
                $q->where(function ($q2) use ($search) {
                    $q2->where('name', 'like', "%{$search}%")
                        ->orWhere('code', 'like', "%{$search}%");
                });
            })
            ->orderBy('name')
            ->paginate(10)
            ->withQueryString();

        return Inertia::render('medicines/index', [
            'medicines' => $medicines,
            'filters' => [
                'search' => $search,
            ],
            'categories' => \App\Models\Category::query()->orderBy('name')->get(['id', 'name']),
        ]);
    }

    public function store(StoreMedicineRequest $request): RedirectResponse
    {
        $this->authorize('create', Medicine::class);

        Medicine::create($request->validated());

        return redirect()->back()->with('success', 'Medicine created');
    }

    public function update(UpdateMedicineRequest $request, Medicine $medicine): RedirectResponse
    {
        $this->authorize('update', $medicine);

        $medicine->update($request->validated());

        return redirect()->back()->with('success', 'Medicine updated');
    }

    public function destroy(Medicine $medicine): RedirectResponse
    {
        $this->authorize('delete', $medicine);

        $medicine->delete();

        return redirect()->back()->with('success', 'Medicine deleted');
    }
}
