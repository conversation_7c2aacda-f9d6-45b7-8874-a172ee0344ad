<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreMedicineRequest;
use App\Http\Requests\UpdateMedicineRequest;
use App\Models\Medicine;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;

class MedicineController extends Controller
{
    public function index(Request $request): Response
    {
        $this->authorize('viewAny', Medicine::class);

        $search = (string) $request->query('search', '');
        $medicines = Medicine::query()
            ->with(['category:id,name', 'supplier:id,name'])
            ->when($search !== '', function ($q) use ($search) {
                $q->where(function ($q2) use ($search) {
                    $q2->where('name', 'like', "%{$search}%")
                        ->orWhere('code', 'like', "%{$search}%");
                });
            })
            ->orderBy('name')
            ->paginate(10)
            ->withQueryString();

        return Inertia::render('medicines/index', [
            'medicines' => $medicines,
            'filters' => [
                'search' => $search,
            ],
            'categories' => \App\Models\Category::query()->orderBy('name')->get(['id', 'name']),
        ]);
    }

    public function store(StoreMedicineRequest $request): RedirectResponse
    {
        $this->authorize('create', Medicine::class);

        Medicine::create($request->validated());

        return redirect()->back()->with('success', 'Medicine created');
    }

    public function update(UpdateMedicineRequest $request, Medicine $medicine): RedirectResponse
    {
        $this->authorize('update', $medicine);

        $medicine->update($request->validated());

        return redirect()->back()->with('success', 'Medicine updated');
    }

    public function destroy(Medicine $medicine): RedirectResponse
    {
        $this->authorize('delete', $medicine);

        $medicine->delete();

        return redirect()->back()->with('success', 'Medicine deleted');
    }

    /**
     * Export medicines to CSV
     */
    public function export(): StreamedResponse
    {
        $this->authorize('viewAny', Medicine::class);

        $medicines = Medicine::with(['category:id,name', 'supplier:id,name'])
            ->orderBy('name')
            ->get();

        $csvData = [];
        $csvData[] = [
            'Name',
            'Code',
            'Category',
            'Supplier',
            'Stock Quantity',
            'Purchase Price',
            'Selling Price',
            'Created At'
        ];

        foreach ($medicines as $medicine) {
            $csvData[] = [
                $medicine->name,
                $medicine->code,
                $medicine->category?->name ?? '',
                $medicine->supplier?->name ?? '',
                $medicine->stock_quantity,
                $medicine->purchase_price,
                $medicine->selling_price,
                $medicine->created_at->format('Y-m-d H:i:s')
            ];
        }

        $filename = 'medicines_export_' . date('Y-m-d_H-i-s') . '.csv';
        $handle = fopen('php://temp', 'r+');

        foreach ($csvData as $row) {
            fputcsv($handle, $row);
        }

        rewind($handle);
        $csv = stream_get_contents($handle);
        fclose($handle);

        return response()->streamDownload(function () use ($csv) {
            echo $csv;
        }, $filename, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
        ]);
    }

    /**
     * Download import template
     */
    public function downloadTemplate(): StreamedResponse
    {
        $templateData = [];
        $templateData[] = [
            'Name',
            'Code',
            'Category Name',
            'Supplier Name',
            'Stock Quantity',
            'Purchase Price',
            'Selling Price'
        ];

        // Add sample data
        $templateData[] = [
            'Sample Medicine',
            'SAMPLE001',
            'General',
            'Sample Supplier',
            '100',
            '10.50',
            '15.00'
        ];

        $filename = 'medicines_import_template.csv';
        $handle = fopen('php://temp', 'r+');

        foreach ($templateData as $row) {
            fputcsv($handle, $row);
        }

        rewind($handle);
        $csv = stream_get_contents($handle);
        fclose($handle);

        return response()->streamDownload(function () use ($csv) {
            echo $csv;
        }, $filename, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"'
        ]);
    }

    /**
     * Import medicines from CSV
     */
    public function import(Request $request): RedirectResponse
    {
        $this->authorize('create', Medicine::class);

        $request->validate([
            'file' => ['required', 'file', 'mimes:csv,txt', 'max:2048']
        ]);

        $file = $request->file('file');
        $handle = fopen($file->getRealPath(), 'r');

        // Skip header row
        fgetcsv($handle);

        $imported = 0;
        $errors = [];

        while (($data = fgetcsv($handle)) !== false) {
            if (count($data) < 7) {
                continue; // Skip incomplete rows
            }

            try {
                // Find or create category
                $categoryName = trim($data[2]);
                $category = \App\Models\Category::firstOrCreate(['name' => $categoryName]);

                // Find or create supplier if provided
                $supplierName = trim($data[3]);
                $supplier = null;
                if (!empty($supplierName)) {
                    $supplier = \App\Models\Supplier::firstOrCreate([
                        'name' => $supplierName
                    ], [
                        'contact_person' => '',
                        'phone' => '',
                        'email' => '',
                        'address' => ''
                    ]);
                }

                // Create medicine
                Medicine::create([
                    'name' => trim($data[0]),
                    'code' => trim($data[1]),
                    'category_id' => $category->id,
                    'supplier_id' => $supplier?->id,
                    'stock_quantity' => (int) ($data[4] ?? 0),
                    'purchase_price' => (float) ($data[5] ?? 0),
                    'selling_price' => (float) ($data[6] ?? 0),
                ]);

                $imported++;
            } catch (\Exception $e) {
                $errors[] = "Row " . ($imported + count($errors) + 2) . ": " . $e->getMessage();
            }
        }

        fclose($handle);

        if (count($errors) > 0) {
            return redirect()->back()
                ->with('success', "Imported {$imported} medicines")
                ->with('errors', $errors);
        }

        return redirect()->back()->with('success', "Successfully imported {$imported} medicines");
    }
}
