import * as React from 'react';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover';

interface DatePickerProps {
    date?: Date;
    onSelect?: (date: Date | undefined) => void;
    placeholder?: string;
    className?: string;
    disabled?: boolean;
}

export function DatePicker({
    date,
    onSelect,
    placeholder = 'Pick a date',
    className,
    disabled,
}: DatePickerProps) {
    return (
        <Popover>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    data-empty={!date}
                    disabled={disabled}
                    className={cn(
                        'w-[280px] justify-start text-left font-normal data-[empty=true]:text-muted-foreground',
                        className,
                    )}
                >
                    <CalendarIcon />
                    {date ? format(date, 'PPP') : <span>{placeholder}</span>}
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
                <Calendar mode="single" selected={date} onSelect={onSelect} />
            </PopoverContent>
        </Popover>
    );
}

// Demo component that matches shadcn docs exactly
export function DatePickerDemo() {
    const [date, setDate] = React.useState<Date>();

    return (
        <Popover>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    data-empty={!date}
                    className="data-[empty=true]:text-muted-foreground w-[280px] justify-start text-left font-normal"
                >
                    <CalendarIcon />
                    {date ? format(date, 'PPP') : <span>Pick a date</span>}
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
                <Calendar mode="single" selected={date} onSelect={setDate} />
            </PopoverContent>
        </Popover>
    );
}

interface DateRangePickerProps {
    startDate?: Date;
    endDate?: Date;
    onStartDateSelect?: (date: Date | undefined) => void;
    onEndDateSelect?: (date: Date | undefined) => void;
    startPlaceholder?: string;
    endPlaceholder?: string;
    className?: string;
    disabled?: boolean;
}

export function DateRangePicker({
    startDate,
    endDate,
    onStartDateSelect,
    onEndDateSelect,
    startPlaceholder = 'Start date',
    endPlaceholder = 'End date',
    className,
    disabled,
}: DateRangePickerProps) {
    return (
        <div className={cn('flex items-center space-x-2', className)}>
            <DatePicker
                date={startDate}
                onSelect={onStartDateSelect}
                placeholder={startPlaceholder}
                disabled={disabled}
                className="w-auto"
            />
            <span className="text-muted-foreground">to</span>
            <DatePicker
                date={endDate}
                onSelect={onEndDateSelect}
                placeholder={endPlaceholder}
                disabled={disabled}
                className="w-auto"
            />
        </div>
    );
}
