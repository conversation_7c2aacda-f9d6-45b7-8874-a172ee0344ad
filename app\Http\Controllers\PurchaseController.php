<?php

namespace App\Http\Controllers;

use App\Http\Requests\StorePurchaseRequest;
use App\Models\Medicine;
use App\Models\Purchase;
use App\Models\Supplier;
use App\Services\PurchaseService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class PurchaseController extends Controller
{
    public function __construct(
        private PurchaseService $purchaseService
    ) {}

    /**
     * Display a listing of purchases.
     */
    public function index(Request $request): Response
    {
        $this->authorize('viewAny', Purchase::class);

        $search = (string) $request->query('search', '');
        $purchases = Purchase::query()
            ->with(['supplier:id,name'])
            ->when($search !== '', function ($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                    ->orWhereHas('supplier', function ($q2) use ($search) {
                        $q2->where('name', 'like', "%{$search}%");
                    });
            })
            ->orderBy('purchased_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        return Inertia::render('purchases/index', [
            'purchases' => $purchases,
            'filters' => [
                'search' => $search,
            ],
        ]);
    }

    /**
     * Show the form for creating a new purchase.
     */
    public function create(): Response
    {
        $this->authorize('create', Purchase::class);

        return Inertia::render('purchases/create', [
            'suppliers' => Supplier::query()->orderBy('name')->get(['id', 'name']),
            'medicines' => Medicine::query()->orderBy('name')->get(['id', 'name', 'code']),
        ]);
    }

    /**
     * Store a newly created purchase.
     */
    public function store(StorePurchaseRequest $request): RedirectResponse
    {
        $this->authorize('create', Purchase::class);

        $purchase = $this->purchaseService->createPurchase($request->validated());

        return redirect()->route('purchases.show', $purchase)
            ->with('success', 'Purchase order created successfully');
    }

    /**
     * Display the specified purchase.
     */
    public function show(Purchase $purchase): Response
    {
        $this->authorize('view', $purchase);

        $purchase->load(['supplier', 'items.medicine']);

        return Inertia::render('purchases/show', [
            'purchase' => $purchase,
        ]);
    }

    /**
     * Remove the specified purchase.
     */
    public function destroy(Purchase $purchase): RedirectResponse
    {
        $this->authorize('delete', $purchase);

        $purchase->delete();

        return redirect()->route('purchases.index')
            ->with('success', 'Purchase deleted successfully');
    }
}
