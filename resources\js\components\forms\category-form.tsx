'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from '@inertiajs/react';
import { Plus } from 'lucide-react';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const formSchema = z.object({
    name: z.string().min(1, { message: 'Please select a category name.' }),
});

// Predefined medicine categories
const MEDICINE_CATEGORIES = [
    'Painkillers',
    'Antibiotics',
    'Antihistamines',
    'Antacids',
    'Vitamins & Supplements',
    'Antipyretics',
    'Anti-inflammatory',
    'Cough & Cold',
    'Digestive Health',
    'Heart & Blood Pressure',
    'Diabetes Medications',
    'Skin Care',
    'Eye Care',
    'Respiratory',
    'Antifungal',
    'Antiviral',
    'Hormones',
    'Mental Health',
    'Vaccines',
    'First Aid',
] as const;

interface CategoryFormProps {
    trigger?: React.ReactNode;
}

export function CategoryForm({ trigger }: CategoryFormProps) {
    const [open, setOpen] = useState(false);

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            name: '',
        },
    });

    function onSubmit(values: z.infer<typeof formSchema>) {
        router.post('/categories', values, {
            onSuccess: () => {
                setOpen(false);
                form.reset();
            },
            onError: (errors) => {
                // Handle validation errors from server
                if (errors.name) {
                    form.setError('name', { message: errors.name });
                }
            },
        });
    }

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                {trigger || (
                    <Button>
                        <Plus className="mr-2 h-4 w-4" />
                        New Category
                    </Button>
                )}
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>Add New Category</DialogTitle>
                    <DialogDescription>Select a medicine category to add to your inventory system.</DialogDescription>
                </DialogHeader>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                        <FormField
                            control={form.control}
                            name="name"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Category Name</FormLabel>
                                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                                        <FormControl>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select a medicine category" />
                                            </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                            {MEDICINE_CATEGORIES.map((category) => (
                                                <SelectItem key={category} value={category}>
                                                    {category}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <div className="flex justify-end space-x-2">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={() => {
                                    setOpen(false);
                                    form.reset();
                                }}
                            >
                                Cancel
                            </Button>
                            <Button type="submit" disabled={form.formState.isSubmitting}>
                                {form.formState.isSubmitting ? 'Adding...' : 'Add Category'}
                            </Button>
                        </div>
                    </form>
                </Form>
            </DialogContent>
        </Dialog>
    );
}
