import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import type { ReportsFilters } from '@/types/reports';
import { router } from '@inertiajs/react';
import { useState } from 'react';

interface DateRangeFilterProps {
    filters: ReportsFilters;
}

export default function DateRangeFilter({ filters }: DateRangeFilterProps) {
    const [startDate, setStartDate] = useState(filters.start_date);
    const [endDate, setEndDate] = useState(filters.end_date);

    const handleFilterUpdate = () => {
        router.get(
            window.location.pathname,
            {
                start_date: startDate,
                end_date: endDate,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    return (
        <Card>
            <CardHeader>
                <CardTitle>Report Period</CardTitle>
                <CardDescription>Select date range for analysis</CardDescription>
            </CardHeader>
            <CardContent>
                <div className="flex items-end space-x-4">
                    <div>
                        <Label htmlFor="start_date">Start Date</Label>
                        <Input id="start_date" type="date" value={startDate} onChange={(e) => setStartDate(e.target.value)} />
                    </div>
                    <div>
                        <Label htmlFor="end_date">End Date</Label>
                        <Input id="end_date" type="date" value={endDate} onChange={(e) => setEndDate(e.target.value)} />
                    </div>
                    <Button onClick={handleFilterUpdate}>Update Report</Button>
                </div>
            </CardContent>
        </Card>
    );
}
