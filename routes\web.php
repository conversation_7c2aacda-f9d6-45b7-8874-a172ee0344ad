<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    Route::resource('categories', \App\Http\Controllers\CategoryController::class)->only(['index', 'store', 'update', 'destroy']);
    Route::resource('suppliers', \App\Http\Controllers\SupplierController::class)->only(['index', 'store', 'update', 'destroy']);
    Route::resource('medicines', \App\Http\Controllers\MedicineController::class)->only(['index', 'store', 'update', 'destroy']);

    // Nested batch routes for medicines
    Route::resource('medicines.batches', \App\Http\Controllers\BatchController::class)->only(['index', 'store', 'update', 'destroy']);

    // Purchase management routes
    Route::resource('purchases', \App\Http\Controllers\PurchaseController::class)->only(['index', 'create', 'store', 'show', 'destroy']);

    // POS (Point of Sale) routes
    Route::get('pos', [\App\Http\Controllers\SaleController::class, 'index'])->name('pos.index');
    Route::get('pos/search', [\App\Http\Controllers\SaleController::class, 'search'])->name('pos.search');
    Route::post('pos/sale', [\App\Http\Controllers\SaleController::class, 'store'])->name('pos.store');
    Route::get('pos/receipt/{sale}', [\App\Http\Controllers\SaleController::class, 'receipt'])->name('pos.receipt');

    // Alerts routes
    Route::get('alerts', [\App\Http\Controllers\AlertController::class, 'index'])->name('alerts.index');
    Route::get('api/alerts/summary', [\App\Http\Controllers\AlertController::class, 'summary'])->name('alerts.summary');
    Route::get('api/alerts/critical', [\App\Http\Controllers\AlertController::class, 'critical'])->name('alerts.critical');

    // Reports routes
    Route::get('reports', [\App\Http\Controllers\ReportsController::class, 'index'])->name('reports.index');
    Route::get('api/reports/sales-data', [\App\Http\Controllers\ReportsController::class, 'salesData'])->name('reports.sales-data');
    Route::get('api/reports/top-medicines', [\App\Http\Controllers\ReportsController::class, 'topMedicines'])->name('reports.top-medicines');

    // Customer management routes
    Route::resource('customers', \App\Http\Controllers\CustomerController::class)->only(['index', 'store', 'show', 'update', 'destroy']);
    Route::get('api/customers/search', [\App\Http\Controllers\CustomerController::class, 'search'])->name('customers.search');
});

require __DIR__ . '/settings.php';
require __DIR__ . '/auth.php';
