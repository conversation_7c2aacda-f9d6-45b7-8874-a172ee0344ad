'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { cn } from '@/lib/utils';
import type { Medicine } from '@/types/medicines';
import { Link } from '@inertiajs/react';
import { AlertTriangle, Edit, Eye, MoreHorizontal, Package, Plus, Trash2, TrendingUp } from 'lucide-react';

interface MedicinesTableProps {
    medicines: Medicine[];
    onDelete?: (id: number) => void;
    isAdmin?: boolean;
    className?: string;
}

export function MedicinesTable({ medicines, onDelete, isAdmin = false, className }: MedicinesTableProps) {
    const formatPrice = (price: number) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(price);
    };

    const getStockStatus = (medicine: Medicine) => {
        if (medicine.stock_quantity === 0) {
            return { status: 'out_of_stock', label: 'Out of Stock', variant: 'destructive' as const };
        } else if (medicine.stock_quantity <= medicine.min_stock_level) {
            return { status: 'low_stock', label: 'Low Stock', variant: 'destructive' as const };
        } else if (medicine.stock_quantity <= medicine.min_stock_level * 2) {
            return { status: 'medium_stock', label: 'Medium Stock', variant: 'secondary' as const };
        } else {
            return { status: 'in_stock', label: 'In Stock', variant: 'default' as const };
        }
    };

    const handleDelete = (id: number, name: string) => {
        if (confirm(`Are you sure you want to delete "${name}"? This action cannot be undone.`)) {
            onDelete?.(id);
        }
    };

    if (medicines.length === 0) {
        return (
            <Card className={className}>
                <CardContent className="flex flex-col items-center justify-center py-16">
                    <Package className="mb-4 h-16 w-16 text-muted-foreground" />
                    <h3 className="mb-2 text-lg font-semibold">No medicines found</h3>
                    <p className="mb-4 text-center text-muted-foreground">
                        No medicines match your current filters. Try adjusting your search criteria.
                    </p>
                    <Button asChild>
                        <Link href="/medicines/create">
                            <Plus className="mr-2 h-4 w-4" />
                            Add Medicine
                        </Link>
                    </Button>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card className={className}>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    Medicines ({medicines.length})
                </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
                <div className="overflow-x-auto">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Medicine</TableHead>
                                <TableHead>Code</TableHead>
                                <TableHead>Category</TableHead>
                                <TableHead>Supplier</TableHead>
                                <TableHead className="text-center">Stock</TableHead>
                                <TableHead className="text-right">Purchase Price</TableHead>
                                <TableHead className="text-right">Selling Price</TableHead>
                                <TableHead className="text-center">Actions</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {medicines.map((medicine) => {
                                const stockStatus = getStockStatus(medicine);
                                return (
                                    <TableRow key={medicine.id}>
                                        <TableCell>
                                            <div className="flex items-center gap-3">
                                                <div className="flex-1">
                                                    <div className="font-medium">{medicine.name}</div>
                                                    {medicine.description && (
                                                        <div className="max-w-xs truncate text-sm text-muted-foreground">{medicine.description}</div>
                                                    )}
                                                </div>
                                                {stockStatus.status === 'out_of_stock' && <AlertTriangle className="h-4 w-4 text-destructive" />}
                                                {stockStatus.status === 'low_stock' && <TrendingUp className="h-4 w-4 text-orange-500" />}
                                            </div>
                                        </TableCell>
                                        <TableCell>
                                            <Badge variant="outline" className="font-mono text-xs">
                                                {medicine.code}
                                            </Badge>
                                        </TableCell>
                                        <TableCell>
                                            <div className="text-sm">{medicine.category?.name || 'Uncategorized'}</div>
                                        </TableCell>
                                        <TableCell>
                                            <div className="text-sm">{medicine.supplier?.name || 'No supplier'}</div>
                                        </TableCell>
                                        <TableCell className="text-center">
                                            <div className="flex flex-col items-center gap-1">
                                                <span
                                                    className={cn(
                                                        'font-medium',
                                                        stockStatus.status === 'out_of_stock' && 'text-destructive',
                                                        stockStatus.status === 'low_stock' && 'text-orange-500',
                                                    )}
                                                >
                                                    {medicine.stock_quantity}
                                                </span>
                                                <Badge variant={stockStatus.variant} className="text-xs">
                                                    {stockStatus.label}
                                                </Badge>
                                            </div>
                                        </TableCell>
                                        <TableCell className="text-right font-medium">{formatPrice(medicine.purchase_price)}</TableCell>
                                        <TableCell className="text-right font-medium">{formatPrice(medicine.selling_price)}</TableCell>
                                        <TableCell>
                                            <div className="flex items-center justify-center gap-2">
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/medicines/${medicine.id}/batches`}>
                                                        <Eye className="mr-1 h-3 w-3" />
                                                        Batches
                                                    </Link>
                                                </Button>

                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                                        <DropdownMenuSeparator />
                                                        <DropdownMenuItem asChild>
                                                            <Link href={`/medicines/${medicine.id}`}>
                                                                <Eye className="mr-2 h-4 w-4" />
                                                                View Details
                                                            </Link>
                                                        </DropdownMenuItem>
                                                        {isAdmin && (
                                                            <>
                                                                <DropdownMenuItem asChild>
                                                                    <Link href={`/medicines/${medicine.id}/edit`}>
                                                                        <Edit className="mr-2 h-4 w-4" />
                                                                        Edit Medicine
                                                                    </Link>
                                                                </DropdownMenuItem>
                                                                <DropdownMenuSeparator />
                                                                <DropdownMenuItem
                                                                    onClick={() => handleDelete(medicine.id, medicine.name)}
                                                                    className="text-destructive focus:text-destructive"
                                                                >
                                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                                    Delete Medicine
                                                                </DropdownMenuItem>
                                                            </>
                                                        )}
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                );
                            })}
                        </TableBody>
                    </Table>
                </div>
            </CardContent>
        </Card>
    );
}
